"""Pipeline to inject various errors into Python code snippets and verify them."""

import random
import ast
from typing import Dict, <PERSON>, <PERSON><PERSON>, List
import astor
from runtime_verifier import hybrid_verify


# ------------------------
# Error Injection Functions
# ------------------------


def inject_key_error(tree):
    """
    Replace an existing dictionary key access with a missing key to trigger KeyError
    """
    candidates = [n for n in ast.walk(tree) if isinstance(n, ast.Subscript)]
    if not candidates:
        return tree, None
    node = random.choice(candidates)
    if isinstance(node.slice, ast.Constant):
        old_value = node.slice.value
        node.slice = ast.Constant(value="missing_key")
        return tree, ("KeyError", old_value)
    return tree, None


def inject_index_error(tree):
    """
    Access an out-of-range list index to trigger IndexError
    """
    candidates = [n for n in ast.walk(tree) if isinstance(n, ast.Subscript)]
    if not candidates:
        return tree, None
    node = random.choice(candidates)
    if isinstance(node.slice, ast.Constant) and isinstance(node.value, ast.Name):
        node.slice = ast.Constant(value=9999)  # likely out-of-range
        return tree, ("IndexError", node.value.id)
    return tree, None


def inject_attribute_error(tree):
    """
    Call a non-existent method on an object to trigger AttributeError
    """
    candidates = [n for n in ast.walk(tree) if isinstance(n, ast.Call)]
    if not candidates:
        return tree, None
    node = random.choice(candidates)
    if isinstance(node.func, ast.Attribute):
        old_attr = node.func.attr
        node.func.attr = "non_existent_method"
        return tree, ("AttributeError", old_attr)
    return tree, None


def inject_zero_division(tree):
    """
    Replace a denominator with 0 to trigger ZeroDivisionError
    """
    candidates = [
        n
        for n in ast.walk(tree)
        if isinstance(n, ast.BinOp) and isinstance(n.op, ast.Div)
    ]
    if not candidates:
        return tree, None
    node = random.choice(candidates)
    node.right = ast.Constant(value=0)
    return tree, ("ZeroDivisionError", "denominator")


def inject_file_not_found(tree):
    """
    Replace file paths in open() calls with a non-existent file to trigger FileNotFoundError
    """
    candidates = [
        n
        for n in ast.walk(tree)
        if isinstance(n, ast.Call)
        and isinstance(n.func, ast.Name)
        and n.func.id == "open"
    ]
    if not candidates:
        return tree, None
    node = random.choice(candidates)
    if node.args:
        old_path = astor.to_source(node.args[0]).strip()
        node.args[0] = ast.Constant(value="non_existent_file.txt")
        return tree, ("FileNotFoundError", old_path)
    return tree, None


def inject_name_error(tree):
    """Introduce a typo in a variable usage to raise NameError."""
    # Collect assigned variable names
    assigned = set()
    for n in ast.walk(tree):
        if isinstance(n, ast.Assign):
            for t in n.targets:
                if isinstance(t, ast.Name):
                    assigned.add(t.id)
        elif isinstance(n, ast.AnnAssign) and isinstance(n.target, ast.Name):
            assigned.add(n.target.id)
    if not assigned:
        return tree, None
    # Find usages (Name nodes not in assignment context)
    usages = [n for n in ast.walk(tree) if isinstance(n, ast.Name) and n.id in assigned]
    if not usages:
        return tree, None
    node = random.choice(usages)
    original = node.id
    # Simple student-like typo: drop last char or add one
    if len(original) > 1:
        mutated = original[:-1]  # drop last char
    else:
        mutated = original + "x"
    node.id = mutated
    return tree, ("NameError", f"{original}->{mutated}")


def inject_off_by_one(tree):
    """Adjust a range() upper bound to create off-by-one logical bug (no exception)."""
    for n in ast.walk(tree):
        if (
            isinstance(n, ast.Call)
            and isinstance(n.func, ast.Name)
            and n.func.id == "range"
            and len(n.args) in (1, 2)
        ):
            # mutate only first candidate to keep single edit
            if (
                len(n.args) == 1
                and isinstance(n.args[0], ast.Constant)
                and isinstance(n.args[0].value, int)
            ):
                original = n.args[0].value
                n.args[0] = ast.Constant(value=original + 1)
                return tree, (
                    "LogicErrorOffByOne",
                    f"range({original}) -> range({original+1})",
                )
            elif len(n.args) == 2 and all(
                isinstance(a, ast.Constant) and isinstance(a.value, int) for a in n.args
            ):
                start = n.args[0].value
                stop = n.args[1].value
                n.args[1] = ast.Constant(value=stop + 1)
                return tree, (
                    "LogicErrorOffByOne",
                    f"range({start},{stop}) -> range({start},{stop+1})",
                )
    return tree, None


def inject_wrong_comparison(tree):
    """Flip a comparison operator to invert logic (== <-> !=, < -> > etc.)."""
    swap = {
        ast.Eq: ast.NotEq,
        ast.NotEq: ast.Eq,
        ast.Lt: ast.Gt,
        ast.Gt: ast.Lt,
        ast.LtE: ast.GtE,
        ast.GtE: ast.LtE,
    }
    for n in ast.walk(tree):
        if isinstance(n, ast.Compare) and n.ops:
            op_type = type(n.ops[0])
            if op_type in swap:
                n.ops[0] = swap[op_type]()
                return tree, ("LogicErrorComparison", op_type.__name__)
    return tree, None


def inject_recursion_error(tree):
    """Add a self-call at end of first function without base case."""
    for n in tree.body:
        if isinstance(n, ast.FunctionDef):
            call = ast.Return(
                value=ast.Call(
                    func=ast.Name(id=n.name, ctx=ast.Load()), args=[], keywords=[]
                )
            )
            n.body.append(call)
            return tree, ("RecursionErrorPotential", n.name)
    return tree, None


def inject_unbound_local(tree):
    """Use variable before assignment inside function to raise UnboundLocalError."""
    for n in tree.body:
        if isinstance(n, ast.FunctionDef):
            # Find first assignment to a simple Name
            for idx, stmt in enumerate(n.body):
                if isinstance(stmt, ast.Assign) and any(
                    isinstance(t, ast.Name) for t in stmt.targets
                ):
                    target_name = next(
                        t.id for t in stmt.targets if isinstance(t, ast.Name)
                    )
                    # Insert a print using it before the assignment
                    new_stmt = ast.Expr(
                        value=ast.Call(
                            func=ast.Name(id="print", ctx=ast.Load()),
                            args=[ast.Name(id=target_name, ctx=ast.Load())],
                            keywords=[],
                        )
                    )
                    n.body.insert(idx, new_stmt)
                    return tree, ("UnboundLocalError", target_name)
    return tree, None


def inject_negated_condition(tree):
    """Negate a boolean condition in an if statement to create logic bug."""
    for n in ast.walk(tree):
        if isinstance(n, ast.If):
            # Wrap condition in 'not (...)' if not already unary not
            if not isinstance(n.test, ast.UnaryOp) or not isinstance(
                n.test.op, ast.Not
            ):
                n.test = ast.UnaryOp(op=ast.Not(), operand=n.test)
                return tree, ("LogicErrorNegation", "added not")
    return tree, None


def inject_missing_colon(tree):
    """Remove a colon from a block header (def/if/for/while/class) to cause SyntaxError."""
    for n in ast.walk(tree):
        if isinstance(n, (ast.FunctionDef, ast.For, ast.While, ast.If, ast.ClassDef)):
            # We can't directly remove colon via AST -> do via textual post-process marker.
            # Return a marker so caller can do simple text replacement.
            return tree, (
                "SyntaxErrorMissingColon",
                f"remove colon after {getattr(n, 'name', type(n).__name__)}",
            )
    return tree, None


def inject_mismatched_paren(tree):
    """Signal to drop a closing parenthesis in the source (leads to SyntaxError)."""
    for n in ast.walk(tree):
        if isinstance(n, ast.Call) and n.args:
            return tree, ("SyntaxErrorMismatchedParen", "remove one closing paren")
    return tree, None


def inject_wrong_arg_count(tree):
    """Duplicate one positional argument in a call to cause 
    TypeError at runtime (if callable enforces arity)."""
    for n in ast.walk(tree):
        if isinstance(n, ast.Call) and n.args:
            arg_clone = n.args[0]
            n.args.append(arg_clone)
            return tree, ("TypeErrorArity", "duplicated first positional argument")
    return tree, None


def inject_keyword_typo(tree):
    """Alter a keyword argument name slightly."""
    for n in ast.walk(tree):
        if isinstance(n, ast.Call) and n.keywords:
            kw = n.keywords[0]
            if kw.arg:
                kw.arg = kw.arg + "_x"
                return tree, ("TypeErrorBadKwarg", "keyword name typo")
    return tree, None


def inject_type_concat_mismatch(tree):
    """Change one operand in a BinOp + to a str literal to trigger TypeError if numeric addition."""
    for n in ast.walk(tree):
        if isinstance(n, ast.BinOp) and isinstance(n.op, ast.Add):
            if isinstance(n.left, ast.Constant) and isinstance(
                n.left.value, (int, float)
            ):
                n.right = ast.Constant(value="str")
                return tree, ("TypeErrorBadAdd", "mixed int/str in addition")
            if isinstance(n.right, ast.Constant) and isinstance(
                n.right.value, (int, float)
            ):
                n.left = ast.Constant(value="str")
                return tree, ("TypeErrorBadAdd", "mixed int/str in addition")
    return tree, None


# ------------------------
# Main Pipeline
# ------------------------

# Categorise injectors for deciding verification strategy
INJECTOR_REGISTRY = [
    {"fn": inject_key_error, "category": "runtime"},
    {"fn": inject_index_error, "category": "runtime"},
    {"fn": inject_attribute_error, "category": "runtime"},
    {"fn": inject_zero_division, "category": "runtime"},
    {"fn": inject_file_not_found, "category": "runtime"},
    {"fn": inject_name_error, "category": "runtime"},
    {"fn": inject_wrong_comparison, "category": "logic"},
    {"fn": inject_recursion_error, "category": "runtime"},
    {"fn": inject_unbound_local, "category": "runtime"},
    {"fn": inject_negated_condition, "category": "logic"},
    {"fn": inject_missing_colon, "category": "syntax"},
    {"fn": inject_mismatched_paren, "category": "syntax"},
    {"fn": inject_wrong_arg_count, "category": "runtime"},
    {"fn": inject_keyword_typo, "category": "runtime"},
    {"fn": inject_type_concat_mismatch, "category": "runtime"},
]

ERROR_FUNCTIONS = [entry["fn"] for entry in INJECTOR_REGISTRY]


def _ast_to_source(tree):
    try:
        return astor.to_source(tree)
    except (AttributeError, TypeError, ValueError):
        try:
            return ast.unparse(tree)
        except Exception:  # pylint: disable=broad-except
            return None


def _fallback_textual_injection(code: str):
    lines = code.splitlines()
    for i, line in enumerate(lines):
        if line.strip().startswith(
            ("if ", "for ", "while ", "def ", "class ")
        ) and line.rstrip().endswith(":"):
            lines[i] = line.rstrip()[:-1]
            return "\n".join(lines), {
                "error_type": "SyntaxErrorMissingColon",
                "details": "removed colon",
                "category": "syntax",
            }
    lines.append("_fallback_val = 1/0  # injected")
    return "\n".join(lines), {
        "error_type": "ZeroDivisionError",
        "details": "added 1/0",
        "category": "runtime",
    }


def _apply_syntax_text_transform(original_code: str, info: Tuple[str, str]) -> str:
    etype, _ = info
    code = original_code
    if etype == "SyntaxErrorMissingColon":
        # remove first colon at end of block header line
        lines = code.splitlines()
        for i, line in enumerate(lines):
            if line.rstrip().endswith(":") and any(
                line.lstrip().startswith(x)
                for x in ("def ", "if ", "for ", "while ", "class ")
            ):
                lines[i] = line.rstrip()[:-1]
                return "\n".join(lines)
        return code
    if etype == "SyntaxErrorMismatchedParen":
        # drop last ')' if any
        idx = code.rfind(")")
        if idx != -1:
            return code[:idx] + code[idx + 1 :]
        return code
    return code


def inject_error_into_code(code: str) -> Tuple[str, Dict[str, Any]]:
    """Inject a single error into the given Python code snippet.
    Returns the mutated code and metadata about the injection and verification.
    """
    try:
        ast.parse(code)
    except SyntaxError:
        return code, {
            "error_type": None,
            "injection_method": "input-invalid",
            "error_description": "parse failure",
            "category": "invalid",
        }

    shuffled = INJECTOR_REGISTRY[:]
    random.shuffle(shuffled)
    chosen_info = None
    chosen_tree = None
    chosen_category = None
    for entry in shuffled:
        fn = entry["fn"]
        category = entry["category"]
        tree_copy = ast.parse(code)
        mutated_tree, info = fn(tree_copy)
        if info is not None:
            chosen_tree = mutated_tree
            chosen_info = info
            chosen_category = category
            break

    if chosen_info is None:
        fallback_code, fallback_meta = _fallback_textual_injection(code)
        fallback_meta["error_description"] = fallback_meta["details"]
        return fallback_code, fallback_meta

    etype, edetail = chosen_info

    # For syntax markers, we must re-textually mutate; cannot rely on AST roundtrip.
    if chosen_category == "syntax":
        mutated_code = _apply_syntax_text_transform(code, chosen_info)
        meta = {
            "error_type": etype,
            "error_description": edetail,
            "category": chosen_category,
            "runtime_verification_status": "skipped_syntax",
            "runtime_error_type": None,
            "runtime_error_description": None,
        }
        return mutated_code, meta

    rendered = _ast_to_source(chosen_tree)
    if not rendered:
        fallback_code, fallback_meta = _fallback_textual_injection(code)
        fallback_meta["error_description"] = fallback_meta["details"]
        return fallback_code, fallback_meta

    # Runtime or logic category: verify only runtime ones
    verify_needed = chosen_category == "runtime"
    runtime_meta = {
        "runtime_verification_status": "skipped_logic",
        "runtime_error_type": None,
        "runtime_error_description": None,
    }
    if verify_needed:
        try:
            runtime_meta = hybrid_verify(rendered, etype)
        except Exception as e:  # pylint: disable=broad-except
            runtime_meta = {
                "runtime_verification_status": "verification_failed",
                "runtime_error_type": None,
                "runtime_error_description": f"verification exception: {e}",
            }

    meta = {
        "error_type": etype,
        "error_description": edetail,
        "category": chosen_category,
        **runtime_meta,
    }
    return rendered, meta


def multi_inject_errors(code: str, passes: int = 3) -> Tuple[str, List[Dict[str, Any]]]:
    """Inject multiple errors into the given Python code snippet.

    Args:
        code (str): The original Python code to mutate.
        passes (int, optional): The number of error injection passes to perform. Defaults to 3.

    Returns:
        Tuple[str, List[Dict[str, Any]]]: _description_
    """
    current = code
    metas: List[Dict[str, Any]] = []
    for _ in range(passes):
        mutated, meta = inject_error_into_code(current)
        metas.append(meta)
        current = mutated
    return current, metas


def inject_errors_batch(# The above code is a comment in Python. Comments are used to provide
# explanations or notes within the code for better understanding. In Python,
# comments start with the `#` symbol and can be placed on a separate line or
# at the end of a line of code. Comments are ignored by the Python interpreter
# and do not affect the execution of the code.
snippets, seed=None):
    """Process iterable of code snippets; returns list of (code, metadata)."""
    if seed is not None:
        random.seed(seed)
    results = []
    for snippet in snippets:
        mutated, meta = inject_error_into_code(snippet)
        results.append((mutated, meta))
    return results


# ------------------------
# Example Usage
# ------------------------

CORRECT_CODE = '''
def flatten_list(nested_list):
    """Flattens a nested list."""
    flat_list = []
    for item in nested_list:
        if isinstance(item, list):
            flat_list.extend(flatten_list(item))
        else:
            flat_list.append(item)
    return flat_list

nested = [[1, 2, [3]], 4]
flattened = flatten_list(nested)
print(flattened)
'''


if __name__ == "__main__":
    injected_code, metadata = inject_error_into_code(CORRECT_CODE)
    print("Injected Code:\n", injected_code)
    print("Metadata:\n", metadata)
