"""Metrics persistence and JSON/CSV export utilities"""
import json
import csv
import time
from pathlib import Path
from typing import Dict, List, Optional, Any
import numpy as np
from classifier_lib.train_model import _collect_provenance, _estimate_param_count, _normalise_params


def build_combined_json(
    run_id: str,
    dataset_hash: Optional[str],
    cfg: Dict[str, Any],
    names: List[str],
    sel_scores: np.ndarray,
    cv_means: np.ndarray,
    stds: np.ndarray,
    times: np.ndarray,
    best_name: str,
    cv_details_aug: Dict[str, Any],
    efficiency_data: Dict[str, Any],
    model_info: Dict[str, Any],
    data_info: Dict[str, Any],
    calibration_data: Dict[str, Any],
    boot_ci: Optional[Dict[str, Any]],
    artefact_data: Dict[str, Any],
    best_model_params: Optional[Dict[str, Any]],
    pr_plot_path: Optional[str],
    error_analysis_paths: Dict[str, Optional[str]],
    slices_data: Dict[str, Any],
    do_final_test: bool,
) -> Dict[str, Any]:
    """Build the comprehensive combined JSON structure for metrics persistence."""

    return {
        "run_id": run_id,
        "dataset_hash": dataset_hash,
        "experiment": cfg.get("experiment", {}),
        "selection": {
            "metric": cfg.get("selection", {}).get("metric", "cv"),
            "scores": {n: float(sel_scores[i]) for i, n in enumerate(names)},
            "cv_means": {n: float(cv_means[i]) for i, n in enumerate(names)},
            "cv_stds": {n: float(stds[i]) for i, n in enumerate(names)},
            "times_sec": {n: float(times[i]) for i, n in enumerate(names)},
            "best_model_name": best_name,
        },
        "cv_details": cv_details_aug,
        "efficiency": efficiency_data,
        "model_info": model_info,
        "data_info": data_info,
        "calibration": calibration_data,
        "bootstrap_ci": boot_ci,
        "artefact": artefact_data,
        "best_model_params": best_model_params,
        "pr_curves_plot": pr_plot_path,
        "error_analysis": error_analysis_paths,
        "slices": slices_data,
        "do_final_test": do_final_test,
        "provenance": _collect_provenance(),
    }


def _sanitise_for_json(obj: Any) -> Any:
    """Recursively sanitise data structures for JSON serialisation."""
    if isinstance(obj, Path):
        return str(obj)
    elif isinstance(obj, dict):
        return {key: _sanitise_for_json(value) for key, value in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [_sanitise_for_json(item) for item in obj]
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64, np.float32)):
        return float(obj)
    else:
        return obj


def persist_metrics_json(
    combined_json: Dict[str, Any], run_id: str, subdirs: Dict[str, Path]
) -> Path:
    """Persist the combined metrics JSON file."""
    json_path = subdirs["metrics"] / f"metrics_{run_id}.json"

    # Sanitise the data to handle Path objects and numpy types
    sanitised_json = _sanitise_for_json(combined_json)

    with json_path.open("w", encoding="utf-8") as f:
        json.dump(sanitised_json, f, indent=2)
    print(f"Saved combined metrics JSON -> {json_path}")
    return json_path


def update_aggregate_csv(
    run_id: str,
    dataset_hash: Optional[str],
    best_name: str,
    results_by_name: Dict[str, Any],
    sel_scores: np.ndarray,
    best_idx: int,
    inference_time: Optional[float],
    per_sample_latency: Optional[float],
    artefact_path: Optional[Path],
    artefact_size: Optional[int],
    pre_cal: Optional[Dict[str, Any]],
    post_cal: Optional[Dict[str, Any]],
    subdirs: Dict[str, Path],
) -> None:
    """Update the aggregate CSV with a new row for the best model."""
    try:
        agg_path = subdirs["metrics"] / "aggregate.csv"
        header = [
            "run_id",
            "dataset_hash",
            "best_model",
            "cv_mean",
            "cv_std",
            "selection_score",
            "inference_time_sec",
            "per_sample_latency_sec",
            "artefact_path",
            "artefact_size_bytes",
            "calib_brier_pre",
            "calib_ece_pre",
            "calib_brier_post",
            "calib_ece_post",
        ]

        row = [
            run_id,
            dataset_hash,
            best_name,
            results_by_name[best_name].get("cv_mean_accuracy"),
            results_by_name[best_name].get("cv_std_accuracy"),
            float(sel_scores[best_idx]),
            inference_time,
            per_sample_latency,
            str(artefact_path) if artefact_path else None,
            artefact_size,
            (pre_cal or {}).get("brier_score") if pre_cal else None,
            (pre_cal or {}).get("ece") if pre_cal else None,
            (post_cal or {}).get("brier_score") if post_cal else None,
            (post_cal or {}).get("ece") if post_cal else None,
        ]

        write_header = not agg_path.exists()
        with agg_path.open("a", newline="", encoding="utf-8") as f:
            w = csv.writer(f)
            if write_header:
                w.writerow(header)
            w.writerow(row)

    except Exception as e: #pylint: disable=broad-except
        print(f"WARNING: Failed to update aggregate CSV: {e}")


def create_run_summary(
    run_id: str,
    best_name: str,
    timings: Dict[str, Dict[str, float | None]],
    calibration_plots: Dict[str, Optional[str]],
    json_path: Path,
    subdirs: Dict[str, Path],
) -> None:
    """Create a run summary JSON for quick reporting."""
    run_summary = {
        "run_id": run_id,
        "best_model_name": best_name,
        "training_time_sec_by_model": {
            n: timings.get(n, {}).get("fit_time_sec") for n in timings
        },
        "calibration_plots": calibration_plots,
        "metrics_json": str(json_path),
    }

    summary_path = subdirs["meta"] / f"run_summary_{run_id}.json"

    sanitised_json = _sanitise_for_json(run_summary)
    with summary_path.open("w", encoding="utf-8") as f:
        json.dump(sanitised_json, f, indent=2)


def compute_efficiency_metrics(
    fitted_models: Dict[str, Any], X_test, run_info: Dict[str, Any]
) -> None:
    """Compute and attach efficiency metrics (inference timing, param counts) to run_info."""
    try:

        efficiency = run_info.setdefault("efficiency", {})
        inf_times: Dict[str, float] = {}
        inf_per_sample_ms: Dict[str, float] = {}
        param_counts: Dict[str, Optional[int]] = {}
        params_by_model: Dict[str, Dict[str, Any]] = {}

        # Robust test size detection
        n_test = None
        try:
            n_test = len(X_test)  # works for arrays, lists, sparse
        except Exception: #pylint: disable=broad-except
            try:
                n_test = int(getattr(X_test, "shape", [None])[0]) 
            except Exception: #pylint: disable=broad-except
                n_test = None

        for name, model in fitted_models.items():
            # Inference timing
            t0 = time.perf_counter()
            try:
                _ = model.predict(X_test)
            except Exception: #pylint: disable=broad-except
                try:
                    _ = model.predict_proba(X_test)  
                except Exception: #pylint: disable=broad-except
                    _ = None
            dt = time.perf_counter() - t0
            inf_times[name] = float(dt)
            if n_test and n_test > 0:
                inf_per_sample_ms[name] = float(1000.0 * dt / n_test)

            # Param count and normalised hyperparameters
            try:
                param_counts[name] = _estimate_param_count(model)
            except Exception: #pylint: disable=broad-except
                param_counts[name] = None
            try:
                params_by_model[name] = _normalise_params(model)
            except Exception: #pylint: disable=broad-except
                params_by_model[name] = {}

        efficiency["inference_time_sec_by_model"] = inf_times
        efficiency["inference_time_per_sample_ms_by_model"] = inf_per_sample_ms
        efficiency["param_count_by_model"] = param_counts
        run_info.setdefault("models", {})["params_by_model"] = params_by_model

    except Exception: #pylint: disable=broad-except
        pass


def sanitise_classifier_params(
    results_by_name: Dict[str, Any], fitted_models: Dict[str, Any]
) -> Dict[str, Any]:
    """Augment CV details with sanitised classifier hyperparameters."""

    def _sanitise_params(d: Dict) -> Dict:
        out = {}
        for k, v in d.items():
            try:
                json.dumps(v)
                out[k] = v
            except (TypeError, ValueError):
                out[k] = str(v)
        return out

    cv_details_aug = {}
    for n in results_by_name.keys():
        entry = dict(results_by_name[n])
        try:
            clf = None
            if hasattr(fitted_models.get(n), "named_steps"):
                clf = fitted_models[n].named_steps.get("classifier")
            clf = clf or fitted_models.get(n)
            if hasattr(clf, "get_params"):
                entry["classifier_params"] = _sanitise_params(
                    clf.get_params(deep=False)
                )
        except Exception: #pylint: disable=broad-except
            pass
        cv_details_aug[n] = entry

    return cv_details_aug


def extract_model_info(best_model) -> Dict[str, Any]:
    """Extract basic model and feature information."""
    from classifier_lib._calibration_utils import _get_classes_safe

    try:
        classes_arr = _get_classes_safe(best_model) or []
        n_classes = int(len(classes_arr)) if hasattr(classes_arr, "__len__") else None
    except Exception: #pylint: disable=broad-except
        n_classes = None

    try:
        vec = getattr(best_model, "named_steps", {}).get("tfidf")
        feature_dim = (
            int(len(vec.get_feature_names_out()))
            if hasattr(vec, "get_feature_names_out")
            else None
        )
    except Exception: #pylint: disable=broad-except
        feature_dim = None

    has_proba = bool(hasattr(best_model, "predict_proba"))

    return {
        "n_classes": n_classes,
        "feature_dimension": feature_dim,
        "has_predict_proba": has_proba,
    }


def compute_model_sizes(fitted_models: Dict[str, Any]) -> Dict[str, Optional[int]]:
    """Estimate serialised sizes for all models via temporary files."""
    size_bytes_by_model: Dict[str, Optional[int]] = {}
    try:
        import tempfile as _tmp
        import joblib as _joblib  

        with _tmp.TemporaryDirectory() as d:
            for n, mdl in fitted_models.items():
                try:
                    pth = Path(d) / f"{n.replace(' ', '_').lower()}.joblib"
                    _joblib.dump(mdl, pth)
                    size_bytes_by_model[n] = int(pth.stat().st_size)
                except (
                    OSError,
                    IOError,
                    ValueError,
                    AttributeError,
                    RuntimeError,
                    TypeError,
                ):
                    size_bytes_by_model[n] = None
    except (ImportError, OSError):
        # Ignore failure; sizes remain empty
        pass

    return size_bytes_by_model
