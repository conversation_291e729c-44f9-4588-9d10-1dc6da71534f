from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.neural_network import MLPClassifier
from sklearn.neighbors import KNeighborsClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.base import BaseEstimator, ClassifierMixin, clone
from sklearn.preprocessing import LabelEncoder, StandardScaler, MinMaxScaler

try:
    from xgboost import XGBClassifier
except ImportError: # pylint: disable=broad-except
    XGBClassifier = None

try:
    from sklearn.ensemble import HistGradientBoostingClassifier
except ImportError:  # older scikit-learn
    HistGradientBoostingClassifier = None

try:
    from sklearn.decomposition import TruncatedSVD
except ImportError:
    TruncatedSVD = None

# Optional imbalanced-learn samplers
try:
    from imblearn.over_sampling import RandomOverSampler
    from imblearn.over_sampling import SMOTE
    from imblearn.under_sampling import RandomUnderSampler

    _HAS_IMBLEARN = True
except Exception:  # pylint: disable=broad-except
    RandomOverSampler = None
    SMOTE = None
    RandomUnderSampler = None
    _HAS_IMBLEARN = False

from classifier_lib.config import get_config, get_effective_n_jobs

TFIDF_MAX_FEATURES = 3000

base_tfidf_config = {
    "stop_words": None,
    "token_pattern": r"(?u)[A-Za-z_][A-Za-z0-9_]*",
    "lowercase": False,
    "min_df": 2,
    "max_df": 0.90,
}


def _seed_from_cfg(cfg: dict | None) -> int:
    try:
        return int((cfg or {}).get("general", {}).get("random_state", 25))
    except Exception:
        return 25


def _class_weight_from_cfg(cfg: dict | None):
    try:
        cb = (cfg or {}).get("class_balance", {})
        if not bool(cb.get("use_class_weight", True)):
            return None
        return cb.get("class_weight", "balanced")
    except Exception:
        return "balanced"


def _maybe_sampler(cfg: dict | None):
    """Return an imbalanced-learn sampler instance based on config, or None.
    The sampler is placed BEFORE the classifier in the pipeline and only affects training folds.
    """
    try:
        cb = (cfg or {}).get("class_balance", {}).get("resampling", {})
        if not bool(cb.get("enabled", False)):
            return None
        if not _HAS_IMBLEARN:
            print(
                "[class_balance] Resampling requested but imbalanced-learn not available. Skipping sampler."
            )
            return None
        method = str(cb.get("method", "random_over")).lower()
        strategy = cb.get("strategy", "auto")
        seed = _seed_from_cfg(cfg)
        if cb.get("random_state", None) is not None:
            try:
                seed = int(cb.get("random_state"))
            except Exception:
                pass
        if method == "random_over":
            return RandomOverSampler(
                sampling_strategy=strategy if strategy != "auto" else "auto",
                random_state=seed,
            )
        if method == "smote":
            if SMOTE is None:
                return None
            return SMOTE(
                sampling_strategy=strategy if strategy != "auto" else "auto",
                random_state=seed,
            )
        if method == "random_under":
            return RandomUnderSampler(
                sampling_strategy=strategy if strategy != "auto" else "auto",
                random_state=seed,
            )
        print(
            f"[class_balance] Unknown resampling method '{method}', skipping sampler."
        )
        return None
    except Exception:
        return None


def make_tfidf(
    cfg: dict | None = None, max_feats: int | None = None, ngram_range=(1, 1)
) -> TfidfVectorizer:
    if cfg is None:
        cfg = get_config(None)
    if max_feats is None:
        max_feats = TFIDF_MAX_FEATURES
    feats = cfg.get("features", {})
    analyzer = feats.get("tfidf_analyzer", "word")
    if analyzer == "char_wb":
        char_range = feats.get("char_ngram_range", [3, 5])
        return TfidfVectorizer(
            analyzer="char_wb",
            ngram_range=tuple(char_range),
            max_features=max_feats,
            min_df=2,
            max_df=0.90,
            lowercase=False,
        )
    return TfidfVectorizer(
        max_features=max_feats,
        ngram_range=ngram_range,
        **base_tfidf_config,
    )


class LabelEncodingClassifier(BaseEstimator, ClassifierMixin):
    """Meta-estimator that label-encodes y for the wrapped classifier and exposes original classes_.
    Useful to work around estimators that assume integer-coded labels internally (e.g., some KNN paths).
    """

    def __init__(self, base_estimator: BaseEstimator):
        self.base_estimator = base_estimator
        self._le: LabelEncoder | None = None
        self.classes_ = None
        # Initialise to satisfy linters and make attributes explicit
        self.estimator_: BaseEstimator | None = None

    def fit(self, X, y):
        self._le = LabelEncoder()
        y_enc = self._le.fit_transform(y)
        self.classes_ = getattr(self._le, "classes_", None)
        self.estimator_ = clone(self.base_estimator)
        self.estimator_.fit(X, y_enc)
        return self

    def predict(self, X):
        y_enc_pred = self.estimator_.predict(X)
        if self._le is not None:
            return self._le.inverse_transform(y_enc_pred)
        return y_enc_pred

    def predict_proba(self, X):
        if hasattr(self.estimator_, "predict_proba"):
            proba = self.estimator_.predict_proba(X)
            # proba columns correspond to encoded classes in ascending order of ints (0..K-1)
            return proba
        raise AttributeError("Underlying estimator does not support predict_proba")

    def decision_function(self, X):
        if hasattr(self.estimator_, "decision_function"):
            return self.estimator_.decision_function(X)
        raise AttributeError("Underlying estimator does not support decision_function")


def _steps_logistic_regression(cfg: dict | None = None):
    seed = _seed_from_cfg(cfg)
    cw = _class_weight_from_cfg(cfg)
    sampler = _maybe_sampler(cfg)
    steps = [
        ("scaler", StandardScaler()),
    ]
    if sampler is not None:
        steps.append(("sampler", sampler))
    steps.append(
        (
            "classifier",
            LogisticRegression(
                solver="liblinear",
                random_state=seed,
                max_iter=2000,
                C=0.1,
                class_weight=cw,
            ),
        )
    )
    return steps


def _steps_random_forest(cfg: dict | None = None):
    seed = _seed_from_cfg(cfg)
    cw = _class_weight_from_cfg(cfg)
    n_jobs = get_effective_n_jobs(cfg)
    sampler = _maybe_sampler(cfg)
    steps = []
    # Random Forest doesn't need scaling, but we can add it for consistency
    if sampler is not None:
        steps.append(("sampler", sampler))
    steps.append(
        (
            "classifier",
            RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=seed,
                n_jobs=n_jobs,
                class_weight=cw,
            ),
        ),
    )
    return steps


def _steps_svm_rbf(cfg: dict | None = None):
    seed = _seed_from_cfg(cfg)
    sampler = _maybe_sampler(cfg)
    steps = []
    if sampler is not None:
        steps.append(("sampler", sampler))
    steps.extend(
        [
            ("scaler", StandardScaler()),
            (
                "classifier",
                SVC(
                    kernel="rbf",
                    C=1.0,
                    gamma="scale",
                    class_weight=_class_weight_from_cfg(cfg),
                    probability=True,
                    random_state=seed,
                ),
            ),
        ]
    )
    return steps


def _steps_svm_linear(cfg: dict | None = None):
    seed = _seed_from_cfg(cfg)
    sampler = _maybe_sampler(cfg)
    steps = [
        ("scaler", StandardScaler()),
    ]
    if sampler is not None:
        steps.append(("sampler", sampler))
    steps.append(
        (
            "classifier",
            SVC(
                kernel="linear",
                C=1.0,
                class_weight=_class_weight_from_cfg(cfg),
                probability=True,
                random_state=seed,
            ),
        ),
    )
    return steps


def _steps_naive_bayes(cfg: dict | None = None):
    sampler = _maybe_sampler(cfg)
    steps = [
        ("scaler", MinMaxScaler()),  # GaussianNB works better with scaled features
    ]
    if sampler is not None:
        steps.append(("sampler", sampler))
    steps.append(("classifier", GaussianNB()))  # Use GaussianNB for numerical features
    return steps


def _steps_nn(cfg: dict | None = None):
    seed = _seed_from_cfg(cfg)
    sampler = _maybe_sampler(cfg)
    steps = [
        ("scaler", StandardScaler()),
    ]
    if sampler is not None:
        steps.append(("sampler", sampler))
    steps.append(
        (
            "classifier",
            MLPClassifier(
                hidden_layer_sizes=(100, 50),
                max_iter=300,
                alpha=0.01,
                random_state=seed,
                early_stopping=True,
                validation_fraction=0.2,
            ),
        ),
    )
    return steps


def _steps_gb(cfg: dict | None = None):
    seed = _seed_from_cfg(cfg)
    sampler = _maybe_sampler(cfg)
    steps = []
    if sampler is not None:
        steps.append(("sampler", sampler))
    steps.append(
        (
            "classifier",
            GradientBoostingClassifier(
                n_estimators=50,
                learning_rate=0.15,
                max_depth=4,
                random_state=seed,
                subsample=0.8,
            ),
        ),
    )
    return steps


def _steps_knn(cfg: dict | None = None):
    sampler = _maybe_sampler(cfg)
    steps = [
        ("scaler", StandardScaler()),
    ]
    if sampler is not None:
        steps.append(("sampler", sampler))
    steps.append(
        (
            "classifier",
            LabelEncodingClassifier(
                KNeighborsClassifier(
                    n_neighbors=5,
                    weights="distance",
                    metric="euclidean",  # Changed from cosine to euclidean for numerical features
                )
            ),
        ),
    )
    return steps


def _steps_decision_tree(cfg: dict | None = None):
    seed = _seed_from_cfg(cfg)
    cw = _class_weight_from_cfg(cfg)
    sampler = _maybe_sampler(cfg)
    steps = []
    if sampler is not None:
        steps.append(("sampler", sampler))
    steps.append(
        (
            "classifier",
            DecisionTreeClassifier(
                max_depth=10,
                min_samples_split=10,
                min_samples_leaf=5,
                class_weight=cw,
                random_state=seed,
            ),
        ),
    )
    return steps


def _steps_xgb(cfg: dict | None = None):
    if XGBClassifier is None:
        return None
    n_jobs = get_effective_n_jobs(cfg)
    seed = _seed_from_cfg(cfg)
    sampler = _maybe_sampler(cfg)
    steps = []
    if sampler is not None:
        steps.append(("sampler", sampler))
    steps.append(
        (
            "classifier",
            XGBClassifier(
                n_estimators=150,
                learning_rate=0.2,
                max_depth=6,
                subsample=0.8,
                colsample_bytree=0.8,
                objective="multi:softprob",
                eval_metric="mlogloss",
                tree_method="hist",
                n_jobs=n_jobs,
                random_state=seed,
            ),
        ),
    )
    return steps


def _steps_hgb(cfg: dict | None = None):
    if HistGradientBoostingClassifier is None:
        return None
    seed = _seed_from_cfg(cfg)
    sampler = _maybe_sampler(cfg)
    steps = []
    if sampler is not None:
        steps.append(("sampler", sampler))
    steps.append(
        (
            "classifier",
            HistGradientBoostingClassifier(
                learning_rate=0.2,
                max_depth=None,
                max_leaf_nodes=31,
                l2_regularization=0.0,
                early_stopping=True,
                random_state=seed,
            ),
        ),
    )
    return steps


def get_steps(classifier_type: str, cfg: dict | None = None):
    match classifier_type:
        case "logistic_regression":
            return _steps_logistic_regression(cfg)
        case "random_forest":
            return _steps_random_forest(cfg)
        case "svm_rbf":
            return _steps_svm_rbf(cfg)
        case "svm_linear":
            return _steps_svm_linear(cfg)
        case "naive_bayes":
            return _steps_naive_bayes(cfg)
        case "neural_network":
            return _steps_nn(cfg)
        case "gradient_boosting":
            return _steps_gb(cfg)
        case "knn":
            return _steps_knn(cfg)
        case "decision_tree":
            return _steps_decision_tree(cfg)
        case "xgboost":
            steps = _steps_xgb(cfg)
            if steps is None:
                raise ValueError(
                    "XGBoost not available. Install xgboost or exclude this model."
                )
            return steps
        case "hist_gradient_boosting":
            steps = _steps_hgb(cfg)
            if steps is None:
                raise ValueError(
                    "HistGradientBoosting not available (requires recent scikit-learn)."
                )
            return steps
        case _:
            raise ValueError(f"Unknown classifier type: {classifier_type}")
