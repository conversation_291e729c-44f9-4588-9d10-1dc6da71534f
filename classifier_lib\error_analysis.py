"""Error analysis and slice metrics utilities"""

from collections import Counter
import csv
import json
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from scipy import stats
import numpy as np
from statsmodels.stats.contingency_tables import mcnemar
import pandas as pd
from sklearn.metrics import (
    classification_report,
    confusion_matrix,
    accuracy_score,
    f1_score,
    balanced_accuracy_score,
)


def export_error_analysis(
    y_test,
    y_test_pred,
    X_test,
    y_train,
    test_metadata,
    run_id: str,
    subdirs: Dict[str, Path],
    cfg: Dict[str, Any],
) -> Dict[str, Optional[str]]:
    """Export comprehensive error analysis including per-class metrics, confusions, and slices.

    Returns:
        dict with paths to generated CSV files
    """
    try:

        # Per-class metrics
        per_class_csv = _export_per_class_metrics(y_test, y_test_pred, run_id, subdirs)

        # Confusion matrix analysis
        confusions_csv = _export_confusion_analysis(
            y_test, y_test_pred, run_id, subdirs
        )

        # Misclassifications with feature snippets
        miscls_csv, miscls_shap_csv = _export_misclassifications(
            y_test, y_test_pred, X_test, run_id, subdirs
        )

        # Slice metrics
        slices_csv = _export_slice_metrics(
            y_test, y_test_pred, y_train, test_metadata, run_id, subdirs, cfg
        )

        return {
            "per_class_csv": per_class_csv,
            "confusions_csv": confusions_csv,
            "misclassifications_csv": miscls_csv,
            "misclassifications_shap_csv": miscls_shap_csv,
            "slices_csv": slices_csv,
        }

    except Exception as e:  # pylint: disable=broad-except
        print(f"Error analysis export failed: {e}")
        return {
            "per_class_csv": None,
            "confusions_csv": None,
            "misclassifications_csv": None,
            "misclassifications_shap_csv": None,
            "slices_csv": None,
        }


def _export_per_class_metrics(
    y_test, y_test_pred, run_id: str, subdirs: Dict[str, Path]
) -> str:
    """Export per-class precision, recall, f1, and support metrics."""

    rep = classification_report(y_test, y_test_pred, output_dict=True, zero_division=0)
    classes = [
        k for k in rep.keys() if k not in {"accuracy", "macro avg", "weighted avg"}
    ]

    rows = []
    for cls in classes:
        m = rep[cls]
        if isinstance(m, dict):
            rows.append(
                {
                    "class": cls,
                    "precision": m.get("precision"),
                    "recall": m.get("recall"),
                    "f1": m.get("f1-score"),
                    "support": m.get("support"),
                }
            )

    per_class_csv = str(subdirs["tables"] / f"per_class_metrics_{run_id}.csv")
    pd.DataFrame(rows).to_csv(per_class_csv, index=False)
    return per_class_csv


def _export_confusion_analysis(
    y_test, y_test_pred, run_id: str, subdirs: Dict[str, Path]
) -> str:
    """Export top confusion pairs analysis."""

    rep = classification_report(y_test, y_test_pred, output_dict=True, zero_division=0)
    classes = [
        k for k in rep.keys() if k not in {"accuracy", "macro avg", "weighted avg"}
    ]

    cm = confusion_matrix(y_test, y_test_pred, labels=classes)
    pairs = []
    for i, a in enumerate(classes):
        for j, b in enumerate(classes):
            if i != j and cm[i, j] > 0:
                pairs.append({"actual": a, "pred": b, "count": int(cm[i, j])})

    pairs = sorted(pairs, key=lambda d: d["count"], reverse=True)
    confusions_csv = str(subdirs["tables"] / f"top_confusions_{run_id}.csv")
    pd.DataFrame(pairs).to_csv(confusions_csv, index=False)
    return confusions_csv


def _export_misclassifications(
    y_test, y_test_pred, X_test, run_id: str, subdirs: Dict[str, Path]
) -> Tuple[str, Optional[str]]:
    """Export misclassification samples with feature snippets and optional SHAP enrichment."""

    # Basic misclassifications
    try:
        X_series = (
            X_test.reset_index(drop=True) if hasattr(X_test, "reset_index") else X_test
        )
        y_true_series = (
            y_test.reset_index(drop=True) if hasattr(y_test, "reset_index") else y_test
        )
    except Exception:  # pylint: disable=broad-except
        X_series, y_true_series = X_test, y_test

    sample_rows = []
    for idx, (yt, yp) in enumerate(zip(list(y_true_series), list(y_test_pred))):
        if yt != yp:
            feat = (
                str(X_series[idx])
                if not hasattr(X_series, "iloc")
                else str(X_series.iloc[idx])
            )
            sample_rows.append(
                {"i": idx, "actual": yt, "pred": yp, "snippet": feat[:200]}
            )

    miscls_csv = str(subdirs["tables"] / f"misclassifications_{run_id}.csv")
    pd.DataFrame(sample_rows).head(200).to_csv(miscls_csv, index=False)

    # SHAP enrichment if available
    miscls_shap_csv = _enrich_with_shap_tokens(sample_rows, run_id, subdirs)

    return miscls_csv, miscls_shap_csv


def _enrich_with_shap_tokens(
    sample_rows: List[Dict], run_id: str, subdirs: Dict[str, Path]
) -> Optional[str]:
    """Enrich misclassifications with SHAP token analysis if available."""
    try:

        shp_dir = subdirs["meta"]  # SHAP tokens are in meta folder
        latest = None
        for p in sorted(shp_dir.glob("shap_tokens_*.json")):
            latest = p

        if not latest or not latest.exists():
            return None

        with latest.open("r", encoding="utf-8") as f:
            _shp = json.load(f)
        per_cls = (
            (_shp.get("per_class_top_tokens") or {}) if isinstance(_shp, dict) else {}
        )

        def _toklist(cls):
            try:
                toks = [
                    t.get("token")
                    for t in per_cls.get(str(cls), [])
                    if isinstance(t, dict)
                ]
                return [t for t in toks if t is not None]
            except (TypeError, ValueError, AttributeError):
                return []

        shap_rows = []
        for r in sample_rows[:200]:
            pred_toks = _toklist(r.get("pred"))
            actual_toks = _toklist(r.get("actual"))

            # Simple overlap counts with the text snippet (token presence)
            snip = r.get("snippet") or ""
            snip_lc = snip.lower()
            pred_hits = sum(
                1 for t in pred_toks[:10] if isinstance(t, str) and t.lower() in snip_lc
            )
            actual_hits = sum(
                1
                for t in actual_toks[:10]
                if isinstance(t, str) and t.lower() in snip_lc
            )

            shap_rows.append(
                {
                    **r,
                    "pred_top_tokens": ", ".join(pred_toks[:5]) if pred_toks else "",
                    "actual_top_tokens": (
                        ", ".join(actual_toks[:5]) if actual_toks else ""
                    ),
                    "pred_token_hits": int(pred_hits),
                    "actual_token_hits": int(actual_hits),
                }
            )

        miscls_shap_csv = str(
            subdirs["tables"] / f"misclassifications_shap_{run_id}.csv"
        )
        pd.DataFrame(shap_rows).to_csv(miscls_shap_csv, index=False)
        return miscls_shap_csv

    except (OSError, IOError, ValueError, TypeError, AttributeError):
        return None


def _export_slice_metrics(
    y_test,
    y_test_pred,
    y_train,
    test_metadata,
    run_id: str,
    subdirs: Dict[str, Path],
    cfg: Dict[str, Any],
) -> Optional[str]:
    """Export slice-based performance metrics (rarity, code length, AST presence)."""
    try:
        # Settings
        slice_cfg = (
            (cfg.get("evaluation", {}) or {}).get("slices", {})
            if isinstance(cfg, dict)
            else {}
        )
        rare_thr = int(slice_cfg.get("rare_threshold", 5))
        len_short = int(slice_cfg.get("length_short_max", 5))
        len_long = int(slice_cfg.get("length_long_min", 21))

        # Build helpers
        y_true = list(y_test)
        y_pred = list(y_test_pred)

        # Train label counts to decide rarity
        train_counts = _get_train_label_counts(y_train)

        # Extract metadata features
        codes, ast_msgs = _extract_metadata_features(test_metadata, len(y_true))

        # Build slice categorisations
        code_len_bucket = [
            _bucket_lines(_line_count(c), len_short, len_long) for c in codes
        ]
        ast_present = [
            "present" if (isinstance(a, str) and a.strip() != "") else "absent"
            for a in ast_msgs
        ]
        rarity = [
            "rare" if train_counts.get(lbl, 0) < rare_thr else "frequent"
            for lbl in y_true
        ]

        # Compute metrics for each slice
        records = []

        # Rarity slices
        for val in ["rare", "frequent"]:
            m = [r == val for r in rarity]
            rec = {"slice_category": "label_frequency", "slice_value": val}
            rec.update(_metrics_for_mask(m, y_true, y_pred))
            records.append(rec)

        # Code length slices
        for val in ["short", "medium", "long"]:
            m = [b == val for b in code_len_bucket]
            rec = {"slice_category": "code_length", "slice_value": val}
            rec.update(_metrics_for_mask(m, y_true, y_pred))
            records.append(rec)

        # AST presence
        for val in ["present", "absent"]:
            m = [a == val for a in ast_present]
            rec = {"slice_category": "ast_message", "slice_value": val}
            rec.update(_metrics_for_mask(m, y_true, y_pred))
            records.append(rec)

        # Persist
        slices_csv = str(subdirs["tables"] / f"slices_{run_id}.csv")
        pd.DataFrame(records).to_csv(slices_csv, index=False)
        print(f"Saved slice metrics -> {slices_csv}")
        return slices_csv

    except Exception as _e:  # pylint: disable=broad-except
        print(f"Slice metrics computation failed/skipped: {_e}")
        return None


def _get_train_label_counts(y_train) -> Dict[str, int]:
    """Get label counts from training data for rarity analysis."""
    try:
        if hasattr(y_train, "value_counts"):
            return {k: int(v) for k, v in y_train.value_counts().items()}
        return dict(Counter(list(y_train)))
    except Exception:  # pylint: disable=broad-except
        return {}


def _extract_metadata_features(test_metadata, n_samples: int) -> Tuple[List, List]:
    """Extract code and AST message features from test metadata."""
    codes = []
    ast_msgs = []

    if test_metadata is not None:
        try:
            # Align order by reset_index if possible
            tmd = (
                test_metadata.reset_index(drop=True)
                if hasattr(test_metadata, "reset_index")
                else test_metadata
            )
            # Attempt to select columns
            codes = (
                list(tmd["code"])
                if hasattr(tmd, "__getitem__") and "code" in getattr(tmd, "columns", [])
                else [None] * n_samples
            )
            ast_msgs = (
                list(tmd["ast_message"])
                if hasattr(tmd, "__getitem__")
                and "ast_message" in getattr(tmd, "columns", [])
                else [None] * n_samples
            )
        except Exception:  # pylint: disable=broad-except
            codes = [None] * n_samples
            ast_msgs = [None] * n_samples
    else:
        codes = [None] * n_samples
        ast_msgs = [None] * n_samples

    return codes, ast_msgs


def _line_count(s: str) -> int:
    """Count non-empty lines in code string."""
    try:
        if not isinstance(s, str):
            s = str(s) if s is not None else ""
        return len([ln for ln in s.splitlines() if ln.strip()])
    except Exception:  # pylint: disable=broad-except
        return 0


def _bucket_lines(n: int, len_short: int, len_long: int) -> str:
    """Bucket line count into short/medium/long categories."""
    if n <= len_short:
        return "short"
    if n >= len_long:
        return "long"
    return "medium"


def _metrics_for_mask(
    mask: List[bool], y_true: List, y_pred: List
) -> Dict[str, float | int | None]:
    """Compute accuracy, macro F1, and balanced accuracy for a boolean mask."""
    try:
        idx = [i for i, m in enumerate(mask) if m]
        if not idx:
            return {
                "n": 0,
                "accuracy": None,
                "macro_f1": None,
                "balanced_accuracy": None,
            }

        yt = [y_true[i] for i in idx]
        yp = [y_pred[i] for i in idx]
        acc = accuracy_score(yt, yp)
        mf1 = f1_score(yt, yp, average="macro", zero_division=0)
        ba = balanced_accuracy_score(yt, yp)

        return {
            "n": len(idx),
            "accuracy": float(acc),
            "macro_f1": float(mf1),
            "balanced_accuracy": float(ba),
        }
    except Exception:  # pylint: disable=broad-except
        return {
            "n": 0,
            "accuracy": None,
            "macro_f1": None,
            "balanced_accuracy": None,
        }


def compute_significance_tests(
    names: List[str],
    sel_scores: np.ndarray,
    fitted_models: Dict[str, Any],
    X_test,
    y_test,
    run_id: str,
    subdirs: Dict[str, Path],
) -> Optional[str]:
    """Compute McNemar and paired t-test between top-2 models."""
    if len(names) < 2:
        return None

    try:
        top2_idx = np.argsort(sel_scores)[-2:][::-1]
        top2 = [names[i] for i in top2_idx]
        preds = {}
        for n in top2:
            preds[n] = fitted_models[n].predict(X_test)

        # McNemar test
        mcn = _maybe_mcnemar(
            np.array(list(y_test)),
            np.array(preds[top2[0]]),
            np.array(preds[top2[1]]),
        )

        # Paired t-test on correctness vectors

        a = (np.array(preds[top2[0]]) == np.array(list(y_test))).astype(int)
        b = (np.array(preds[top2[1]]) == np.array(list(y_test))).astype(int)
        t_stat, t_p = stats.ttest_rel(a, b, nan_policy="omit")

        # Export results

        sig_csv = str(subdirs["tables"] / f"significance_{run_id}.csv")
        with open(sig_csv, "w", newline="", encoding="utf-8") as f:
            w = csv.DictWriter(
                f,
                fieldnames=[
                    "model_a",
                    "model_b",
                    "mcnemar_b01",
                    "mcnemar_b10",
                    "mcnemar_stat",
                    "mcnemar_p",
                    "ttest_t",
                    "ttest_p",
                ],
            )
            w.writeheader()
            w.writerow(
                {
                    "model_a": top2[0],
                    "model_b": top2[1],
                    "mcnemar_b01": (mcn or {}).get("b01") if mcn else None,
                    "mcnemar_b10": (mcn or {}).get("b10") if mcn else None,
                    "mcnemar_stat": (mcn or {}).get("statistic") if mcn else None,
                    "mcnemar_p": (mcn or {}).get("pvalue") if mcn else None,
                    "ttest_t": float(t_stat) if t_stat is not None else None,
                    "ttest_p": float(t_p) if t_p is not None else None,
                }
            )

        print(f"Saved significance results -> {sig_csv}")
        return sig_csv

    except Exception as e:  # pylint: disable=broad-except
        print(f"Significance testing failed: {e}")
        return None


def _maybe_mcnemar(y_true, pred_a, pred_b) -> Optional[Dict[str, float]]:
    """Compute McNemar test between two predictions."""

    # Build 2x2 table of disagreements
    a_correct = pred_a == y_true
    b_correct = pred_b == y_true
    b01 = int((~a_correct & b_correct).sum())
    b10 = int((a_correct & ~b_correct).sum())
    table = [[0, b01], [b10, 0]]
    res = mcnemar(table, exact=False, correction=True)

    return {
        "b01": b01,
        "b10": b10,
        "statistic": float(res.statistic),
        "pvalue": float(res.pvalue),
    }
