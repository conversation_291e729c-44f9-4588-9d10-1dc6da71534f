#!/usr/bin/env python3
"""
Test script to validate the full pipeline with AST features.
"""

import pandas as pd
import numpy as np
from classifier_lib.preprocessing import prepare_features_and_split
from classifier_lib.pipeline_steps import get_steps
from sklearn.pipeline import Pipeline

def create_test_dataset():
    """Create a small test dataset for validation."""
    test_data = [
        {
            'correct_code': 'def hello(): print("Hello")',
            'buggy_code': 'def hello(): print("Hello")',
            'intended_error_type': 'NoError',
            'ast_message': 'NoError',
            'error_description': 'No error'
        },
        {
            'correct_code': 'if True:\n    print("Missing colon")',
            'buggy_code': 'if True\n    print("Missing colon")',
            'intended_error_type': 'SyntaxError',
            'ast_message': 'SyntaxError',
            'error_description': 'Missing colon in if statement'
        },
        {
            'correct_code': 'defined_variable = "hello"\nprint(defined_variable)',
            'buggy_code': 'print(undefined_variable)',
            'intended_error_type': 'NameError',
            'ast_message': 'NameError',
            'error_description': 'Variable not defined'
        },
        {
            'correct_code': 'x = [1, 2, 3]\nprint(x[1])',
            'buggy_code': 'x = [1, 2, 3]\nprint(x[10])',
            'intended_error_type': 'IndexError',
            'ast_message': 'IndexError',
            'error_description': 'List index out of range'
        },
        {
            'correct_code': 'def func():\n    return 42\nresult = func()',
            'buggy_code': 'def func():\n    return 42\nresult = func()',
            'intended_error_type': 'NoError',
            'ast_message': 'NoError',
            'error_description': 'No error'
        },
        {
            'correct_code': 'for i in range(5):\n    print(i)',
            'buggy_code': 'for i in range(5):\nprint(i)',
            'intended_error_type': 'IndentationError',
            'ast_message': 'IndentationError',
            'error_description': 'Indentation error'
        }
    ]

    return pd.DataFrame(test_data)

def test_preprocessing_pipeline():
    """Test the preprocessing pipeline with AST features."""
    print("Testing Preprocessing Pipeline with AST Features")
    print("=" * 60)
    
    # Create test dataset
    df = create_test_dataset()
    print(f"Created test dataset with {len(df)} samples")
    print(f"Label distribution:\n{df['intended_error_type'].value_counts()}")
    
    try:
        # Test preprocessing
        print("\nTesting preprocessing...")
        result = prepare_features_and_split(
            df,
            leakage_safe=True,
            test_size=0.3,
            random_state=42,
            include_no_error=True
        )
        
        X_train, X_test, y_train, y_test, X_all, y_all, df_all, train_df, test_df = result
        
        print(f"Training set shape: {X_train.shape}")
        print(f"Test set shape: {X_test.shape}")
        print(f"Feature columns: {list(X_train.columns)[:5]}...")  # Show first 5 feature names
        
        # Check if features are numerical
        print(f"All features are numerical: {X_train.dtypes.apply(lambda x: np.issubdtype(x, np.number)).all()}")
        
        return X_train, X_test, y_train, y_test
        
    except Exception as e:
        print(f"Error in preprocessing: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_model_pipeline(X_train, X_test, y_train, y_test):
    """Test a simple model pipeline."""
    print("\nTesting Model Pipeline")
    print("=" * 40)
    
    try:
        # Test Random Forest pipeline (doesn't need scaling)
        print("Testing Random Forest pipeline...")
        steps = get_steps("random_forest", cfg=None)
        pipeline = Pipeline(steps)
        
        print(f"Pipeline steps: {[step[0] for step in steps]}")
        
        # Fit the pipeline
        print("Fitting pipeline...")
        pipeline.fit(X_train, y_train)
        
        # Make predictions
        print("Making predictions...")
        train_pred = pipeline.predict(X_train)
        test_pred = pipeline.predict(X_test)
        
        print(f"Training accuracy: {(train_pred == y_train).mean():.3f}")
        print(f"Test accuracy: {(test_pred == y_test).mean():.3f}")
        
        # Test prediction probabilities
        if hasattr(pipeline, 'predict_proba'):
            train_proba = pipeline.predict_proba(X_train)
            print(f"Prediction probabilities shape: {train_proba.shape}")
        
        print("Model pipeline test successful!")
        return True
        
    except Exception as e:
        print(f"Error in model pipeline: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("AST-Based Pipeline Validation")
    print("=" * 60)
    
    # Test preprocessing
    result = test_preprocessing_pipeline()
    if result is None:
        print("Preprocessing test failed!")
        return
    
    X_train, X_test, y_train, y_test = result
    
    # Test model pipeline
    success = test_model_pipeline(X_train, X_test, y_train, y_test)
    
    if success:
        print("\n" + "=" * 60)
        print("✅ All tests passed! AST-based pipeline is working correctly.")
    else:
        print("\n" + "=" * 60)
        print("❌ Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
