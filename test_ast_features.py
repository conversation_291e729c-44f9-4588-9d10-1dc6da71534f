#!/usr/bin/env python3
"""
Test script to validate the new AST-based feature extraction.
"""

import sys
import pandas as pd
from classifier_lib.data import create_feature_text, extract_ast_features

def test_ast_feature_extraction():
    """Test the AST feature extraction with sample code."""
    print("Testing AST Feature Extraction")
    print("=" * 50)
    
    # Test cases with different types of code
    test_cases = [
        {
            'code': '''
def hello_world():
    print("Hello, World!")
    return True
''',
            'description': 'Simple function'
        },
        {
            'code': '''
for i in range(10):
    if i % 2 == 0:
        print(i)
    else:
        continue
''',
            'description': 'Loop with conditional'
        },
        {
            'code': '''
class MyClass:
    def __init__(self, value):
        self.value = value
    
    def get_value(self):
        return self.value
''',
            'description': 'Class definition'
        },
        {
            'code': '''
try:
    x = 1 / 0
except ZeroDivisionError:
    print("Division by zero!")
finally:
    print("Cleanup")
''',
            'description': 'Exception handling'
        },
        {
            'code': '''
# Syntax error - missing colon
if True
    print("This has a syntax error")
''',
            'description': 'Code with syntax error'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}: {test_case['description']}")
        print("-" * 30)
        print(f"Code:\n{test_case['code']}")
        
        # Test direct AST feature extraction
        try:
            features = extract_ast_features(test_case['code'])
            print(f"AST Features extracted: {len(features)} features")
            
            # Show some key features
            key_features = {
                'total_nodes': features.get('total_nodes', 0),
                'function_defs': features.get('function_defs', 0),
                'class_defs': features.get('class_defs', 0),
                'if_statements': features.get('if_statements', 0),
                'for_loops': features.get('for_loops', 0),
                'tree_depth': features.get('tree_depth', 0),
                'has_syntax_error': features.get('has_syntax_error', 0),
                'parse_error': features.get('parse_error', 0)
            }
            print(f"Key features: {key_features}")
            
        except Exception as e:
            print(f"Error extracting AST features: {e}")
        
        # Test create_feature_text function
        try:
            row = {'code': test_case['code']}
            feature_dict = create_feature_text(row)
            print(f"create_feature_text result: {type(feature_dict)} with {len(feature_dict)} features")
            
        except Exception as e:
            print(f"Error with create_feature_text: {e}")
    
    print("\n" + "=" * 50)
    print("AST Feature Extraction Test Complete")

if __name__ == "__main__":
    test_ast_feature_extraction()
