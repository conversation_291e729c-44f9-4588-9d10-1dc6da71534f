"""Creates a classifier pipeline with optional sampling steps"""

# Prefer imblearn Pipeline when available to support sampling steps
try:  # optional dependency
    from imblearn.pipeline import Pipeline

    _HAS_IMBLEARN = True
except Exception:  # pylint: disable=broad-except
    Pipeline = None # pylint: disable=invalid-name
    _HAS_IMBLEARN = False

from sklearn.pipeline import Pipeline as SkPipeline
from classifier_lib.pipeline_steps import (
    get_steps,
)


def create_classifier_pipeline(
    classifier_type="logistic_regression", cfg: dict | None = None
):
    """
    Creates and returns a pipeline for error type classification.
    Supports multiple classifier types for comparison.

    Args:
        classifier_type (str): Type of classifier to use.
        cfg (dict | None): Runtime configuration affecting feature extraction, etc.
    """
    steps = get_steps(classifier_type, cfg)
    # If any step provides fit_resample (imbalanced-learn sampler), use ImbPipeline
    has_sampler = any(hasattr(step, "fit_resample") for name, step in steps)
    if has_sampler and _HAS_IMBLEARN and Pipeline is not None:
        return Pipeline(steps)
    # else fallback to sklearn Pipeline
    return SkPipeline(steps)
