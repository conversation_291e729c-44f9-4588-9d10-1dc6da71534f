import re
import ast
import numpy as np

from classifier_lib.preprocessing import group_error_types
from classifier_lib.config import get_config, get_run_output_dir, create_run_subdirs

def analyse_model_explainability(
    pipeline,
    X_test,
    y_test,
    feature_names=None,
    n_examples=3,
    use_shap=True,
    shap_sample_size=150,
    cfg: dict | None = None,
    run_id: str | None = None,
    model_name: str = "Model",
    all_models_performance: dict | None = None,
):
    """Comprehensive model diagnostics & explainability.

    Features:
    - Feature quality checks (literal numbers, categories)
    - Linear model per-class coefficient inspection
    - Tree/ensemble feature_importances_ + permutation importance fallback
    - Generic permutation importance fallback when neither coef_ nor feature_importances_ present
    - Confidence distribution (if predict_proba exists)
    - Optional SHAP global + limited per-class explanations (sampled for efficiency)

    Notes:
    - SHAP is an optional dependency. Install with `pip install shap` to enable.
    """
    print("\n=== MODEL DIAGNOSTIC ANALYSIS ===")

    # Config
    cfg = cfg or get_config()
    ex_cfg = cfg.get("explainability", {})
    pi_sample = int(ex_cfg.get("permutation_importance_sample", 300))
    shap_bg_size_cfg = int(ex_cfg.get("shap_background_size", 50))
    shap_max_cap = int(ex_cfg.get("shap_max_evals_cap", 4000))
    shap_skip_costly = bool(ex_cfg.get("shap_skip_if_costly", True))
    # Honor config toggle for SHAP if provided without broad exception handling
    if "use_shap" in ex_cfg:
        use_shap = bool(ex_cfg["use_shap"])  # fallback to function arg otherwise
    # Resolve global seed for any stochastic analyses without try/except
    seed_val = (cfg.get("general", {}) or {}).get("random_state", 25)
    if isinstance(seed_val, (int, np.integer)):
        base_seed = int(seed_val)
    elif isinstance(seed_val, str) and seed_val.isdigit():
        base_seed = int(seed_val)
    else:
        base_seed = 25

    # Extract components
    vectorizer = getattr(pipeline, "named_steps", {}).get("tfidf")
    classifier = getattr(pipeline, "named_steps", {}).get("classifier")
    # Build a preprocessor that includes all steps before the classifier (e.g., tfidf -> svd)
    preprocessor = None
    try:
        if hasattr(pipeline, "steps") and len(pipeline.steps) >= 2:
            preprocessor = pipeline[:-1]
    except (AttributeError, TypeError, IndexError):
        preprocessor = None
    if classifier is None:
        print("Pipeline missing 'classifier' step; aborting explainability.")
        return

    # Feature names
    if (
        feature_names is None
        and vectorizer is not None
        and hasattr(vectorizer, "get_feature_names_out")
    ):
        feature_names = vectorizer.get_feature_names_out()
    elif feature_names is None:
        feature_names = np.array(
            [f"f{i}" for i in range(0, getattr(classifier, "n_features_in_", 0))]
        )

    # Basic model info
    try:
        n_classes = len(classifier.classes_)
    except AttributeError:
        n_classes = "unknown"
    print(f"  • Total features: {len(feature_names)}")
    print(f"  • Classes: {n_classes}")

    # ---------------- Feature Quality Diagnostics ----------------
    print("\n=== FEATURE QUALITY DIAGNOSTICS ===")
    literal_numbers = []
    code_keywords = []
    ast_features = []
    for f in feature_names:
        if isinstance(f, str) and (
            f.isdigit() or (f.replace(".", "").isdigit() and f.count(".") <= 1)
        ):
            literal_numbers.append(f)
        if isinstance(f, str) and f.startswith("ast_msg"):
            ast_features.append(f)
        elif isinstance(f, str) and any(
            kw in f for kw in ["def", "if", "for", "while", "class", "import"]
        ):
            code_keywords.append(f)
    print(f"  • AST message features: {len(ast_features)}")
    print(f"  • Code keyword features: {len(code_keywords)}")
    print(f"  • Literal number features: {len(literal_numbers)}")
    if literal_numbers:
        print(
            "  WARNING: Literal numeric tokens may cause overfitting (first 10 shown):"
        )
        for num in literal_numbers[:10]:
            print(f"    • {num}")
        if len(literal_numbers) > 10:
            print(f"    • ... and {len(literal_numbers) - 10} more")

    # ---------------- Contribution Analysis ----------------
    print("\n=== CLASS / FEATURE CONTRIBUTION ANALYSIS ===")
    classes = getattr(classifier, "classes_", [])
    if hasattr(classifier, "coef_"):
        coef = classifier.coef_
        for i, cls in enumerate(classes):
            if len(classes) > 2:
                class_coef = coef[i]
            else:
                class_coef = coef[0] if i == 1 else -coef[0]
            top_idx = np.argsort(class_coef)[-5:][::-1]
            print(f"\n{cls}:")
            issues = []
            for idx in top_idx:
                weight = class_coef[idx]
                print(f"  • {feature_names[idx]}: {weight:.3f}")
                if weight <= 0.01:
                    continue
                fname = feature_names[idx]
                clean = (
                    fname.replace("ast_msg:", "")
                    .replace("desc:", "")
                    .replace("code:", "")
                    if isinstance(fname, str)
                    else str(fname)
                )
                flag = ""
                if fname in literal_numbers:
                    flag = " [OVERFITTING RISK]"
                    issues.append("literal")
                elif len(clean) < 3:
                    flag = " [TOO GENERIC]"
                    issues.append("generic")
                elif clean in ["error", "exception", "traceback"]:
                    flag = " [TOO BROAD]"
                    issues.append("broad")
                print(f"  • {clean[:40]}: {weight:.3f}{flag}")
            if issues:
                print("    Quality concerns: " + ", ".join(sorted(set(issues))))

    elif hasattr(classifier, "feature_importances_"):
        importances = classifier.feature_importances_
        top_k = min(10, len(importances))
        top_idx = np.argsort(importances)[-top_k:][::-1]
        print("Model exposes feature_importances_ (global). Top features:")
        for r, idx in enumerate(top_idx, start=1):
            fname = feature_names[idx] if idx < len(feature_names) else f"f{idx}"
            print(f"  {r:2d}. {str(fname)[:50]:<50} {importances[idx]:.4f}")
        print(
            "Per-class attributions not inherent; using permutation importance for robustness."
        )
        try:
            from sklearn.inspection import permutation_importance

            # Subsample for efficiency
            if hasattr(X_test, "iloc") and len(X_test) > pi_sample:
                X_perm = X_test.iloc[:pi_sample]
                y_perm = y_test.iloc[:pi_sample]
            else:
                X_perm = X_test
                y_perm = y_test
            # Transform to 2D feature matrix using the vectorizer when available
            if (
                vectorizer is not None
                and hasattr(vectorizer, "transform")
                or (preprocessor is not None and hasattr(preprocessor, "transform"))
            ):
                try:
                    X_perm_trans = (
                        preprocessor.transform(X_perm)
                        if preprocessor is not None
                        else vectorizer.transform(X_perm)
                    )
                except (ValueError, AttributeError, RuntimeError, TypeError):
                    X_perm_trans = (
                        vectorizer.transform(X_perm)
                        if vectorizer is not None
                        else X_perm
                    )
                # Ensure dense matrix for permutation_importance
                if hasattr(X_perm_trans, "toarray"):
                    X_perm_trans = X_perm_trans.toarray()
                X_perm_trans = np.asarray(X_perm_trans, dtype=np.float32)
            else:
                # Attempt to coerce to 2D array; if not possible, skip
                X_perm_trans = getattr(X_perm, "values", X_perm)
                X_perm_trans = np.asarray(X_perm_trans, dtype=np.float32)
                if X_perm_trans.ndim == 1:
                    raise ValueError(
                        "Permutation importance requires 2D features; no vectorizer available to transform text."
                    )
            # If feature_names length mismatches transformed features, adjust to generic names
            if (
                feature_names is None
                or len(feature_names) != getattr(X_perm_trans, "shape", [0, 0])[1]
            ):
                feature_names = np.array(
                    [f"f{i}" for i in range(getattr(X_perm_trans, "shape", [0, 0])[1])]
                )
            print(
                "  Computing permutation importance on classifier (balanced_accuracy, 3 repeats) with transformed features..."
            )
            perm = permutation_importance(
                classifier,
                X_perm_trans,
                y_perm,
                n_repeats=3,
                random_state=base_seed,
                scoring="balanced_accuracy",
            )
            p_imp = perm.importances_mean
            top_pi = np.argsort(p_imp)[-top_k:][::-1]
            print("  Top permutation features:")
            for r, idx in enumerate(top_pi, start=1):
                fname = feature_names[idx] if idx < len(feature_names) else f"f{idx}"
                print(f"    {r:2d}. {str(fname)[:50]:<50} {p_imp[idx]:.4f}")
        except (ValueError, RuntimeError) as e:
            print(f"  Permutation importance skipped: {e}")
    else:
        print(
            "Estimator lacks coef_ and feature_importances_. Falling back to permutation importance."
        )
        try:
            from sklearn.inspection import permutation_importance

            # Subsample for efficiency
            if hasattr(X_test, "iloc") and len(X_test) > pi_sample:
                X_perm = X_test.iloc[:pi_sample]
                y_perm = y_test.iloc[:pi_sample]
            else:
                X_perm = X_test
                y_perm = y_test
            # Transform to 2D feature matrix using the vectorizer when available
            if (
                vectorizer is not None
                and hasattr(vectorizer, "transform")
                or (preprocessor is not None and hasattr(preprocessor, "transform"))
            ):
                try:
                    X_perm_trans = (
                        preprocessor.transform(X_perm)
                        if preprocessor is not None
                        else vectorizer.transform(X_perm)
                    )
                except (ValueError, AttributeError, RuntimeError, TypeError):
                    X_perm_trans = (
                        vectorizer.transform(X_perm)
                        if vectorizer is not None
                        else X_perm
                    )
                # Ensure dense matrix for permutation_importance
                if hasattr(X_perm_trans, "toarray"):
                    X_perm_trans = X_perm_trans.toarray()
                X_perm_trans = np.asarray(X_perm_trans, dtype=np.float32)
            else:
                # Attempt to coerce to 2D array; if not possible, skip
                X_perm_trans = getattr(X_perm, "values", X_perm)
                X_perm_trans = np.asarray(X_perm_trans, dtype=np.float32)
                if X_perm_trans.ndim == 1:
                    raise ValueError(
                        """Permutation importance requires 2D features; 
                        no vectorizer available to transform text."""
                    )
            # If feature_names length mismatches transformed features, adjust to generic names
            if (
                feature_names is None
                or len(feature_names) != getattr(X_perm_trans, "shape", [0, 0])[1]
            ):
                feature_names = np.array(
                    [f"f{i}" for i in range(getattr(X_perm_trans, "shape", [0, 0])[1])]
                )
            print(
                "  Computing permutation importance on classifier ",
                "(balanced_accuracy, 3 repeats) with transformed features...",
            )
            perm = permutation_importance(
                classifier,
                X_perm_trans,
                y_perm,
                n_repeats=3,
                random_state=base_seed,
                scoring="balanced_accuracy",
            )
            p_imp = perm.importances_mean
            top_k = min(10, len(p_imp))
            top_idx = np.argsort(p_imp)[-top_k:][::-1]
            print("  Top permutation features:")
            for r, idx in enumerate(top_idx, start=1):
                fname = feature_names[idx] if idx < len(feature_names) else f"f{idx}"
                print(f"    {r:2d}. {str(fname)[:50]:<50} {p_imp[idx]:.4f}")
        except (ValueError, RuntimeError) as e:
            print(f"  Permutation importance failed: {e}")

    # ---------------- Confidence Analysis ----------------
    if hasattr(pipeline, "predict_proba"):
        try:
            probs = pipeline.predict_proba(X_test)
            conf = np.max(probs, axis=1)
            print("\n=== PREDICTION CONFIDENCE ANALYSIS ===")
            print(
                f"  • Very high (>0.9): {(conf > 0.9).sum()}/{len(conf)} ({(conf > 0.9).mean()*100:.1f}%)"
            )
            print(
                f"  • High (0.7-0.9): {((conf > 0.7) & (conf <= 0.9)).sum()}/{len(conf)} ({((conf > 0.7) & (conf <= 0.9)).mean()*100:.1f}%)"
            )
            print(
                f"  • Medium (0.5-0.7): {((conf > 0.5) & (conf <= 0.7)).sum()}/{len(conf)} ({((conf > 0.5) & (conf <= 0.7)).mean()*100:.1f}%)"
            )
            print(
                f"  • Low (<0.5): {(conf <= 0.5).sum()}/{len(conf)} ({(conf <= 0.5).mean()*100:.1f}%)"
            )
            over_mask = conf > 0.95
            if over_mask.sum() > 0:
                y_pred = pipeline.predict(X_test)
                print(f"  Over-confident examples (up to {n_examples}):")
                for j, idx in enumerate(
                    np.where(over_mask)[0][: max(0, int(n_examples))]
                ):
                    try:
                        actual = y_test.iloc[idx]
                    except AttributeError:
                        actual = y_test[idx]
                    try:
                        input_text = X_test.iloc[idx][:100]
                    except AttributeError:
                        input_text = str(X_test[idx])[:100]
                    print(
                        f"    • Example {j+1}: Conf={conf[idx]:.3f} Pred={y_pred[idx]} Actual={actual} | {input_text}..."
                    )
        except (ValueError, RuntimeError, AttributeError) as e:
            print(f"  Confidence analysis skipped: {e}")

    # ---------------- SHAP Explainability ----------------
    if use_shap:
        # Performance-based gate: only run SHAP for top performers
        if not should_run_shap_analysis(model_name, all_models_performance):
            return

        # Allowlist gate: only run SHAP for selected model tags
        allowlist = ex_cfg.get("shap_model_allowlist", [])

        def _infer_model_tag(clf):
            name = type(clf).__name__
            # Linear family (includes linear SVM)
            if (
                name in {"LogisticRegression", "LinearSVC"}
                or (name == "SVC" and getattr(clf, "kernel", None) == "linear")
                or (
                    hasattr(clf, "coef_")
                    and not any(k in name for k in ["Forest", "Boost", "Tree", "XGB"])
                )
            ):
                return "linear"
            if "RandomForest" in name:
                return "random_forest"
            if "XGB" in name:
                return "xgboost"
            if "HistGradientBoosting" in name:
                return "hist_gradient_boosting"
            if name == "GradientBoostingClassifier" or "GradientBoosting" in name:
                return "gradient_boosting"
            if "DecisionTree" in name or name.endswith("TreeClassifier"):
                return "decision_tree"
            if "KNeighbors" in name:
                return "knn"
            if "MultinomialNB" in name or "Naive" in name or name.endswith("NB"):
                return "naive_bayes"
            if "MLP" in name or "Neural" in name:
                return "neural_network"
            return name.lower()

        try:
            allow = True
            if isinstance(allowlist, (list, tuple, set)) and len(allowlist) > 0:
                tag = _infer_model_tag(classifier)
                allow = str(tag).lower() in {str(t).lower() for t in allowlist}
                if not allow:
                    print("\n=== SHAP EXPLAINABILITY (skipped by allowlist) ===")
                    print(
                        f"  Model '{type(classifier).__name__}' tagged as '{tag}' not in shap_model_allowlist: {list(allowlist)}"
                    )
            if not allow:
                pass
            else:
                print("\n=== SHAP EXPLAINABILITY (sampled) ===")
                try:
                    import shap

                    # Recompute vectorized data for sample
                    if hasattr(X_test, "iloc"):
                        X_sample = X_test.iloc[: min(shap_sample_size, len(X_test))]
                    else:
                        X_sample = X_test[: min(shap_sample_size, len(X_test))]
                    if vectorizer is not None:
                        try:
                            X_trans = (
                                preprocessor.transform(X_sample)
                                if preprocessor is not None
                                else vectorizer.transform(X_sample)
                            )
                        except (ValueError, AttributeError, RuntimeError, TypeError):
                            X_trans = vectorizer.transform(X_sample)
                    else:
                        X_trans = X_sample
                    # Detect sparse
                    sparse_input = False
                    try:
                        from scipy import sparse as _sp

                        sparse_input = _sp.issparse(X_trans)
                    except (ImportError, AttributeError, TypeError):
                        # Fallback: many sparse matrices expose toarray()
                        sparse_input = hasattr(X_trans, "toarray")

                    # Safe row count helper to avoid len() on sparse matrices
                    def _n_rows(X):
                        shape = getattr(X, "shape", None)
                        if (
                            shape is not None
                            and hasattr(shape, "__len__")
                            and len(shape) > 0
                        ):
                            try:
                                return int(shape[0])
                            except (TypeError, ValueError):
                                pass
                        try:
                            return len(X)
                        except TypeError:
                            return 0

                    model_name = type(classifier).__name__
                    is_tree = any(
                        k in model_name
                        for k in [
                            "Forest",
                            "GradientBoosting",
                            "HistGradientBoosting",
                            "XGB",
                            "Tree",
                        ]
                    )
                    is_linear = hasattr(classifier, "coef_") and not is_tree
                    bg_size = min(shap_bg_size_cfg, _n_rows(X_trans))
                    if bg_size <= 0:
                        print(
                            "  SHAP skipped: no background data available after transformation."
                        )
                        raise RuntimeError("No SHAP background")
                    used_permutation_algo = False
                    # Build prediction function for generic/permutation explainers
                    if hasattr(classifier, "predict_proba"):
                        predict_func = classifier.predict_proba
                    elif hasattr(classifier, "decision_function"):
                        predict_func = classifier.decision_function
                    else:
                        predict_func = classifier.predict
                    # Prepare a dense background slice for explainers that need it
                    bg_dense = (
                        X_trans[:bg_size].toarray()
                        if sparse_input
                        else X_trans[:bg_size]
                    )
                    if is_tree:
                        # Prepare background data for SHAP and build a robust TreeExplainer
                        bg_dense = (
                            X_trans[:bg_size].toarray()
                            if sparse_input
                            else X_trans[:bg_size]
                        )
                        try:
                            try:
                                # Prefer interventional perturbation and probability output for classifiers
                                explainer = shap.TreeExplainer(
                                    classifier,
                                    data=bg_dense,
                                    feature_perturbation="interventional",
                                    model_output="probability",
                                )
                            except TypeError:
                                # Version-compat fallbacks (older SHAP may not accept model_output or feature_perturbation)
                                try:
                                    explainer = shap.TreeExplainer(
                                        classifier,
                                        data=bg_dense,
                                        feature_perturbation="interventional",
                                    )
                                except TypeError:
                                    explainer = shap.TreeExplainer(
                                        classifier, data=bg_dense
                                    )
                        except (
                            ValueError,
                            AttributeError,
                            RuntimeError,
                            TypeError,
                        ) as e:
                            print(
                                f"  TreeExplainer not available for this model ({e}); falling back to permutation-based SHAP."
                            )
                            try:
                                explainer = shap.Explainer(
                                    predict_func, bg_dense, algorithm="permutation"
                                )
                                used_permutation_algo = True
                            except TypeError:
                                # Older/newer SHAP versions may not accept algorithm kw; fall back to default generic
                                explainer = shap.Explainer(predict_func, bg_dense)
                                used_permutation_algo = True
                    elif is_linear:
                        try:
                            bg_dense = (
                                X_trans[:bg_size].toarray()
                                if sparse_input
                                else X_trans[:bg_size]
                            )
                            explainer = shap.LinearExplainer(classifier, bg_dense)
                        except (ValueError, RuntimeError, AttributeError, TypeError):
                            explainer = shap.Explainer(predict_func, bg_dense)
                    else:
                        print("  Using generic SHAP Explainer (may be slow).")
                        bg_dense = (
                            X_trans[:bg_size].toarray()
                            if sparse_input
                            else X_trans[:bg_size]
                        )
                        explainer = shap.Explainer(predict_func, bg_dense)
                    X_eval = X_trans[: min(shap_sample_size, _n_rows(X_trans))]
                    if sparse_input:
                        X_eval = X_eval.toarray()
                    # Compute SHAP values with dynamic max_evals for permutation algorithm and disable additivity check for tree explainers
                    if used_permutation_algo:
                        n_features = getattr(X_eval, "shape", [0, 0])[1]
                        required = 2 * int(n_features) + 1
                        if shap_skip_costly and required > shap_max_cap:
                            print(
                                f"  Skipping SHAP (permutation) due to cost: requires at least {required} evaluations for {n_features} features (cap={shap_max_cap})."
                            )
                            raise RuntimeError("SHAP skipped due to cost.")
                        shap_values = explainer(X_eval, max_evals=required)
                    else:
                        try:
                            # Disable additivity check to avoid false failures with probability outputs
                            shap_values = explainer(X_eval, check_additivity=False)
                        except TypeError:
                            # Some Explainer.__call__ may not accept the kwarg
                            shap_values = explainer(X_eval)
                    # Extract raw SHAP values robustly
                    vals = (
                        shap_values.values
                        if hasattr(shap_values, "values")
                        else shap_values
                    )
                    per_class_mean = None
                    if isinstance(vals, list):
                        # List of arrays per class: each (n_samples, n_features)
                        try:
                            per_class_mean = np.vstack(
                                [np.mean(np.abs(v), axis=0) for v in vals]
                            )
                            aggregated = np.mean(per_class_mean, axis=0)
                            arr = None
                        except (ValueError, RuntimeError, TypeError):
                            aggregated = np.mean([np.abs(v) for v in vals], axis=0)
                            arr = None
                    else:
                        arr = np.array(vals)
                        if arr.ndim == 3:
                            # (n_samples, n_classes, n_features)
                            per_class_mean = np.mean(np.abs(arr), axis=0)
                            aggregated = np.mean(per_class_mean, axis=0)
                        elif arr.ndim == 2:
                            aggregated = np.mean(np.abs(arr), axis=0)
                        else:
                            aggregated = None
                            print(f"  Unexpected SHAP values shape: {arr.shape}")
                    # Align feature names for transformed feature dimension, if needed
                    if (
                        "aggregated" in locals()
                        and aggregated is not None
                        and feature_names is not None
                        and len(feature_names) != aggregated.shape[0]
                    ):
                        feature_names = np.array(
                            [f"f{i}" for i in range(aggregated.shape[0])]
                        )
                    # Export SHAP token lists (global + per-class)
                    try:
                        from datetime import datetime
                        import json as _json

                        # Skip persistence if inside nested CV inner run
                        in_nested = (
                            bool(
                                cfg.get("evaluation", {})
                                .get("nested_cv", {})
                                .get("in_progress", False)
                            )
                            if isinstance(cfg, dict)
                            else False
                        )
                        top_n = int(ex_cfg.get("shap_top_n_tokens", 20))
                        if aggregated is not None and not in_nested:
                            top_idx = np.argsort(aggregated)[
                                -min(top_n, aggregated.shape[0]) :
                            ][::-1]
                            global_tokens = [
                                {
                                    "token": (
                                        str(feature_names[i])
                                        if i < len(feature_names)
                                        else f"f{i}"
                                    ),
                                    "mean_abs_shap": float(aggregated[i]),
                                }
                                for i in top_idx
                            ]
                            per_class_tokens = {}
                            # Build per-class only if we have per-class means and classes
                            model_classes = list(getattr(classifier, "classes_", []))
                            if (
                                per_class_mean is not None
                                and isinstance(model_classes, list)
                                and len(model_classes) > 0
                            ):
                                for ci, cls in enumerate(model_classes):
                                    vec = per_class_mean[ci]
                                    idxs = np.argsort(vec)[-min(top_n, vec.shape[0]) :][
                                        ::-1
                                    ]
                                    per_class_tokens[str(cls)] = [
                                        {
                                            "token": (
                                                str(feature_names[i])
                                                if i < len(feature_names)
                                                else f"f{i}"
                                            ),
                                            "mean_abs_shap": float(vec[i]),
                                        }
                                        for i in idxs
                                    ]
                            # Use unified output directory structure
                            # Generate run_id if not provided
                            if run_id is None:
                                from datetime import datetime, timezone

                                run_id = datetime.now(timezone.utc).strftime(
                                    "%Y%m%d_%H%M%S"
                                )

                            run_dir = get_run_output_dir(run_id)
                            subdirs = create_run_subdirs(run_dir)
                            out_dir = subdirs["meta"]  # SHAP tokens go in meta folder

                            out_path = out_dir / f"shap_tokens_{run_id}.json"
                            payload = {
                                "model": type(classifier).__name__,
                                "top_n": top_n,
                                "classes": [
                                    str(c) for c in getattr(classifier, "classes_", [])
                                ],
                                "global_top_tokens": global_tokens,
                                "per_class_top_tokens": per_class_tokens,
                                "n_samples_used": int(
                                    getattr(shap_values, "values", vals)[0].shape[0]
                                    if isinstance(vals, list)
                                    else (
                                        arr.shape[0]
                                        if "arr" in locals()
                                        and arr is not None
                                        and hasattr(arr, "shape")
                                        and arr.ndim >= 2
                                        else min(shap_sample_size, len(X_test))
                                    )
                                ),
                            }
                            with out_path.open("w", encoding="utf-8") as f:
                                _json.dump(payload, f, indent=2)
                            print(f"  Saved SHAP token lists -> {out_path}")
                    except (OSError, IOError, ValueError, TypeError) as e:
                        print(f"  WARNING: Failed to persist SHAP tokens: {e}")

                    # Generate SHAP visualisations
                    if aggregated is not None:
                        _generate_shap_visualisations(
                            shap_values, X_sample, feature_names, classifier,
                            model_name, subdirs, run_id
                        )

                        top_k = min(10, aggregated.shape[0])
                        top_idx = np.argsort(aggregated)[-top_k:][::-1]
                        print("  Top SHAP global features (mean |SHAP|):")
                        for r, idx in enumerate(top_idx, start=1):
                            fname = (
                                feature_names[idx]
                                if idx < len(feature_names)
                                else f"f{idx}"
                            )
                            print(
                                f"    {r:2d}. {str(fname)[:50]:<50} {aggregated[idx]:.6f}"
                            )
                        risky = [
                            feature_names[i]
                            for i in top_idx
                            if feature_names is not None
                            and isinstance(feature_names[i], str)
                            and feature_names[i] in literal_numbers
                        ]
                        if risky:
                            print(
                                "  WARNING: Literal-number features among top SHAP attributions:"
                            )
                            for r in risky:
                                print(f"    • {r}")
                    if (
                        "arr" in locals()
                        and arr is not None
                        and arr.ndim == 3
                        and len(classes) > 0
                    ):
                        mean_per_class = np.mean(np.abs(arr), axis=0)
                        subset_feats = (
                            top_idx[:3]
                            if "top_idx" in locals()
                            else list(range(min(3, mean_per_class.shape[1])))
                        )
                        for ci, cls in enumerate(classes[:3]):
                            print(f"\n  Class '{cls}' top SHAP features (subset):")
                            for fidx in subset_feats:
                                fname = (
                                    feature_names[fidx]
                                    if fidx < len(feature_names)
                                    else f"f{fidx}"
                                )
                                print(
                                    f"    • {str(fname)[:40]:<40} {mean_per_class[ci, fidx]:.6f}"
                                )
                except ImportError:
                    print("  SHAP not installed. Run: pip install shap")
                except (ValueError, RuntimeError, AttributeError, MemoryError) as e:
                    print(f"  SHAP explainability skipped: {e}")
        except (ValueError, TypeError, AttributeError):
            # If anything unexpected happens in allowlist logic, don't block other analyses
            print(
                """\n=== SHAP EXPLAINABILITY (skipped) ===
                Allowlist check failed; skipping SHAP to be safe."""
            )


def _generate_shap_visualisations(shap_values, X_sample, feature_names, classifier,
                                 model_name, subdirs, run_id):
    """Generate comprehensive SHAP visualisations

    Creates publication-quality plots including:
    - Summary plot (global feature importance)
    - Waterfall plots (individual predictions)
    - Dependence plots (feature interactions)
    - Bar plots (mean absolute SHAP values)
    """
    try:
        import matplotlib.pyplot as plt
        import shap

        # Ensure we have the figures subdirectory
        figures_dir = subdirs.get("figures")
        if figures_dir is None:
            print("  WARNING: No figures directory available for SHAP plots")
            return

        # Set up matplotlib for publication quality
        plt.style.use('default')
        plt.rcParams.update({
            'font.size': 10,
            'axes.titlesize': 12,
            'axes.labelsize': 10,
            'xtick.labelsize': 9,
            'ytick.labelsize': 9,
            'legend.fontsize': 9,
            'figure.titlesize': 14,
            'figure.dpi': 300,
            'savefig.dpi': 300,
            'savefig.bbox': 'tight'
        })

        # Extract SHAP values array
        if hasattr(shap_values, 'values'):
            values_array = shap_values.values
        elif isinstance(shap_values, list) and len(shap_values) > 0:
            values_array = shap_values[0] if len(shap_values) == 1 else np.array(shap_values)
        else:
            values_array = shap_values

        # Ensure we have proper feature names
        if feature_names is None:
            n_features = values_array.shape[-1] if values_array.ndim > 1 else len(values_array)
            feature_names = [f"feat_{i}" for i in range(n_features)]

        print(f"  Generating SHAP visualisations for {model_name}...")

        # 1. Summary Plot (Global Feature Importance)
        try:
            plt.figure(figsize=(10, 8))
            if values_array.ndim == 3:  # Multi-class
                # For multi-class, show summary for the first class or aggregate
                shap.summary_plot(values_array[:, :, 0], X_sample,
                                feature_names=feature_names, show=False, max_display=15)
            else:
                shap.summary_plot(values_array, X_sample,
                                feature_names=feature_names, show=False, max_display=15)

            plt.title(f'SHAP Summary Plot - {model_name}', fontsize=14, pad=20)
            plt.tight_layout()
            summary_path = figures_dir / f"shap_summary_{model_name.replace(' ', '_').lower()}_{run_id}.png"
            plt.savefig(summary_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"    • Summary plot saved -> {summary_path}")
        except Exception as e:
            print(f"    • Summary plot failed: {e}")

        # 2. Bar Plot (Mean Absolute SHAP Values)
        try:
            plt.figure(figsize=(10, 6))
            if values_array.ndim == 3:
                # Multi-class: average across classes
                mean_shap = np.mean(np.abs(values_array), axis=(0, 2))
            else:
                mean_shap = np.mean(np.abs(values_array), axis=0)

            # Get top 15 features
            top_indices = np.argsort(mean_shap)[-15:]
            top_features = [feature_names[i] for i in top_indices]
            top_values = mean_shap[top_indices]

            plt.barh(range(len(top_features)), top_values)
            plt.yticks(range(len(top_features)), [f[:30] for f in top_features])
            plt.xlabel('Mean |SHAP Value|')
            plt.title(f'Top 15 Features by Mean |SHAP| - {model_name}')
            plt.tight_layout()

            bar_path = figures_dir / f"shap_bar_{model_name.replace(' ', '_').lower()}_{run_id}.png"
            plt.savefig(bar_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"    • Bar plot saved -> {bar_path}")
        except Exception as e: #pylint: disable=broad-except
            print(f"    • Bar plot failed: {e}")

        # 3. Waterfall Plots (Individual Predictions) - Sample a few examples
        try:
            n_waterfall = min(3, X_sample.shape[0])
            for i in range(n_waterfall):
                plt.figure(figsize=(10, 6))

                if values_array.ndim == 3:
                    # Multi-class: use the first class
                    sample_shap = values_array[i, :, 0]
                else:
                    sample_shap = values_array[i, :]

                # Create waterfall plot manually (simpler than shap.waterfall_plot)
                # Sort features by absolute SHAP value
                abs_shap = np.abs(sample_shap)
                sorted_indices = np.argsort(abs_shap)[-10:]  # Top 10 features

                sorted_features = [feature_names[idx][:20] for idx in sorted_indices]
                sorted_values = sample_shap[sorted_indices]

                colours = ['red' if v < 0 else 'blue' for v in sorted_values]
                plt.barh(range(len(sorted_features)), sorted_values, color=colours)
                plt.yticks(range(len(sorted_features)), sorted_features)
                plt.xlabel('SHAP Value')
                plt.title(f'SHAP Waterfall - {model_name} (Sample {i+1})')
                plt.axvline(x=0, color='black', linestyle='-', alpha=0.3)
                plt.tight_layout()

                waterfall_path = figures_dir / f"shap_waterfall_{model_name.replace(' ', '_').lower()}_sample{i+1}_{run_id}.png"
                plt.savefig(waterfall_path, dpi=300, bbox_inches='tight')
                plt.close()
                print(f"    • Waterfall plot {i+1} saved -> {waterfall_path}")
        except Exception as e:
            print(f"    • Waterfall plots failed: {e}")

        # 4. Dependence Plot (Feature Interactions) - Top 2 features
        try:
            if values_array.ndim == 3:
                plot_values = values_array[:, :, 0]  # Use first class for multi-class
            else:
                plot_values = values_array

            mean_abs_shap = np.mean(np.abs(plot_values), axis=0)
            top_2_features = np.argsort(mean_abs_shap)[-2:]

            for i, feat_idx in enumerate(top_2_features):
                plt.figure(figsize=(8, 6))
                shap.dependence_plot(feat_idx, plot_values, X_sample,
                                   feature_names=feature_names, show=False)
                plt.title(f'SHAP Dependence - {feature_names[feat_idx]} - {model_name}')
                plt.tight_layout()

                dep_path = figures_dir / f"shap_dependence_{model_name.replace(' ', '_').lower()}_feat{i+1}_{run_id}.png"
                plt.savefig(dep_path, dpi=300, bbox_inches='tight')
                plt.close()
                print(f"    • Dependence plot {i+1} saved -> {dep_path}")
        except Exception as e:
            print(f"    • Dependence plots failed: {e}")

        # 5. Generate SHAP Analysis Report
        _generate_shap_report(values_array, X_sample, feature_names, model_name, subdirs, run_id)

        print(f"SHAP visualisations complete for {model_name}")

    except ImportError:
        print("  WARNING: matplotlib not available for SHAP visualisations")
    except Exception as e:
        print(f"  WARNING: SHAP visualisation generation failed: {e}")


def _generate_shap_report(shap_values, X_sample, feature_names, model_name, subdirs, run_id):
    """Generate a comprehensive SHAP analysis report """
    try:
        import json
        from datetime import datetime, timezone

        # Calculate comprehensive statistics
        if shap_values.ndim == 3:  # Multi-class
            mean_abs_shap = np.mean(np.abs(shap_values), axis=(0, 2))
            std_abs_shap = np.std(np.abs(shap_values), axis=(0, 2))
            max_abs_shap = np.max(np.abs(shap_values), axis=(0, 2))
        else:
            mean_abs_shap = np.mean(np.abs(shap_values), axis=0)
            std_abs_shap = np.std(np.abs(shap_values), axis=0)
            max_abs_shap = np.max(np.abs(shap_values), axis=0)

        # Get top features
        top_20_indices = np.argsort(mean_abs_shap)[-20:][::-1]

        # Build comprehensive report
        report = {
            "metadata": {
                "model_name": model_name,
                "run_id": run_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "n_samples": int(X_sample.shape[0]),
                "n_features": int(len(feature_names)),
                "shap_values_shape": list(shap_values.shape),
                "analysis_type": "multi_class" if shap_values.ndim == 3 else "binary_or_regression"
            },
            "global_importance": {
                "top_20_features": [
                    {
                        "rank": int(i + 1),
                        "feature_name": str(feature_names[idx]),
                        "mean_abs_shap": float(mean_abs_shap[idx]),
                        "std_abs_shap": float(std_abs_shap[idx]),
                        "max_abs_shap": float(max_abs_shap[idx]),
                        "feature_index": int(idx)
                    }
                    for i, idx in enumerate(top_20_indices)
                ],
                "total_importance": float(np.sum(mean_abs_shap)),
                "top_10_importance_ratio": float(np.sum(mean_abs_shap[top_20_indices[:10]]) / np.sum(mean_abs_shap))
            },
            "feature_statistics": {
                "mean_importance": float(np.mean(mean_abs_shap)),
                "median_importance": float(np.median(mean_abs_shap)),
                "std_importance": float(np.std(mean_abs_shap)),
                "max_importance": float(np.max(mean_abs_shap)),
                "min_importance": float(np.min(mean_abs_shap)),
                "importance_concentration": float(np.sum(mean_abs_shap[top_20_indices[:5]]) / np.sum(mean_abs_shap))
            },
            "ast_feature_analysis": _analyze_ast_feature_patterns(feature_names, mean_abs_shap),
            "visualisations_generated": [
                f"shap_summary_{model_name.replace(' ', '_').lower()}_{run_id}.png",
                f"shap_bar_{model_name.replace(' ', '_').lower()}_{run_id}.png",
                f"shap_waterfall_{model_name.replace(' ', '_').lower()}_sample1_{run_id}.png",
                f"shap_waterfall_{model_name.replace(' ', '_').lower()}_sample2_{run_id}.png",
                f"shap_waterfall_{model_name.replace(' ', '_').lower()}_sample3_{run_id}.png",
                f"shap_dependence_{model_name.replace(' ', '_').lower()}_feat1_{run_id}.png",
                f"shap_dependence_{model_name.replace(' ', '_').lower()}_feat2_{run_id}.png"
            ]
        }

        # Save comprehensive report
        report_path = subdirs["meta"] / f"shap_analysis_report_{model_name.replace(' ', '_').lower()}_{run_id}.json"
        with report_path.open("w", encoding="utf-8") as f:
            json.dump(report, f, indent=2)

        print(f"    • SHAP analysis report saved -> {report_path}")

    except Exception as e:
        print(f"    • SHAP report generation failed: {e}")


def _analyze_ast_feature_patterns(feature_names, importance_scores):
    """Analyze patterns in AST feature importance for code error detection."""
    patterns = {
        "structural_features": [],
        "control_flow_features": [],
        "complexity_features": [],
        "syntactic_features": [],
        "other_features": []
    }

    # Categorize features by type
    for i, (name, score) in enumerate(zip(feature_names, importance_scores)):
        name_str = str(name).lower()

        if any(kw in name_str for kw in ['node', 'tree', 'depth', 'total']):
            patterns["structural_features"].append({"name": str(name), "importance": float(score), "rank": i})
        elif any(kw in name_str for kw in ['if', 'for', 'while', 'try', 'except', 'loop']):
            patterns["control_flow_features"].append({"name": str(name), "importance": float(score), "rank": i})
        elif any(kw in name_str for kw in ['complexity', 'nesting', 'branch']):
            patterns["complexity_features"].append({"name": str(name), "importance": float(score), "rank": i})
        elif any(kw in name_str for kw in ['assign', 'call', 'attr', 'name', 'literal']):
            patterns["syntactic_features"].append({"name": str(name), "importance": float(score), "rank": i})
        else:
            patterns["other_features"].append({"name": str(name), "importance": float(score), "rank": i})

    # Sort each category by importance
    for category in patterns:
        patterns[category] = sorted(patterns[category], key=lambda x: x["importance"], reverse=True)

    # Add summary statistics
    patterns["summary"] = {
        "most_important_category": max(patterns.keys(),
                                     key=lambda k: sum(f["importance"] for f in patterns[k]) if patterns[k] else 0),
        "category_importance_totals": {
            cat: sum(f["importance"] for f in features)
            for cat, features in patterns.items() if cat != "summary"
        }
    }

    return patterns


def should_run_shap_analysis(model_name, all_models_performance=None, top_n=3):
    """Determine if SHAP analysis is worthwhile based on model performance and type.

    Args:
        model_name: Name of the model to analyze
        all_models_performance: Dict of {model_name: performance_score} for ranking
        top_n: Only run SHAP if model is in top N performers

    Returns:
        bool: True if SHAP analysis should be run
    """
    # If we have performance data, only run SHAP for top performers
    if all_models_performance:
        # Sort models by performance (descending)
        sorted_models = sorted(all_models_performance.items(), key=lambda x: x[1], reverse=True)
        top_models = [name for name, _ in sorted_models[:top_n]]

        if model_name not in top_models:
            print(f"\n=== SHAP EXPLAINABILITY (skipped) ===")
            print(f"  {model_name} not in top {top_n} performers")
            print(f"  Top {top_n}: {', '.join(top_models)}")
            return False

    return True


def generate_comparative_shap_analysis(all_models, model_performance, X_test, y_test,
                                      cfg, subdirs, run_id, top_n=3):
    """Generate comparative SHAP analysis across top performing models.

    This creates a side-by-side comparison of feature importance across
    the best performing models.
    """
    try:
        if not model_performance:
            print("No model performance data available for comparative SHAP analysis")
            return

        # Get top N performing models
        sorted_models = sorted(model_performance.items(), key=lambda x: x[1], reverse=True)
        top_models = [(name, score) for name, score in sorted_models[:top_n] if name in all_models]

        if len(top_models) < 2:
            print(f"Need at least 2 models for comparative analysis, found {len(top_models)}")
            return

        print(f"\n=== COMPARATIVE SHAP ANALYSIS (Top {len(top_models)} Models) ===")

        # Run SHAP analysis for each top model
        shap_results = {}
        for model_name, cv_score in top_models:
            print(f"Running comparative SHAP analysis for {model_name} (CV: {cv_score:.3f})...")

            # Run dedicated comparative SHAP that bypasses restrictions
            _run_comparative_shap_for_model(
                all_models[model_name], X_test, y_test, model_name,
                cfg, subdirs, run_id
            )

        # Create comparative summary
        _create_comparative_shap_summary(top_models, subdirs, run_id)

    except Exception as e:
        print(f"Comparative SHAP analysis failed: {e}")


def _run_comparative_shap_for_model(pipeline, X_test, y_test, model_name, cfg, subdirs, run_id):
    """Run SHAP analysis for a single model in comparative analysis, bypassing restrictions."""
    try:
        import shap

        print(f"  Generating SHAP analysis for {model_name}...")

        # Get sample size from config
        shap_sample_size = cfg.get("explainability", {}).get("shap_sample_size", 150)
        sample_size = min(shap_sample_size, len(X_test))

        # Sample data for SHAP analysis
        if sample_size < len(X_test):
            import numpy as np
            np.random.seed(42)  # For reproducibility
            sample_indices = np.random.choice(len(X_test), sample_size, replace=False)
            X_sample = X_test.iloc[sample_indices] if hasattr(X_test, 'iloc') else X_test[sample_indices]
            y_sample = y_test.iloc[sample_indices] if hasattr(y_test, 'iloc') else y_test[sample_indices]
        else:
            X_sample = X_test
            y_sample = y_test

        # Get feature names - avoid accessing pipeline.feature_names_in_ directly
        feature_names = None
        try:
            # Try to get feature names from the vectorizer step
            vectorizer = getattr(pipeline, "named_steps", {}).get("tfidf")
            if vectorizer is not None and hasattr(vectorizer, "get_feature_names_out"):
                feature_names = vectorizer.get_feature_names_out()
        except (AttributeError, TypeError):
            pass

        if feature_names is None:
            if hasattr(X_sample, 'columns'):
                feature_names = list(X_sample.columns)
            else:
                n_features = X_sample.shape[1]
                feature_names = [f"feat_{i}" for i in range(n_features)]

        # Create SHAP explainer based on model type
        classifier = pipeline.named_steps.get("classifier", pipeline)

        try:
            # Convert X_sample to numpy array for SHAP
            if hasattr(X_sample, 'values'):
                X_sample_array = X_sample.values
            else:
                X_sample_array = X_sample

            # Try different explainer types based on model
            model_name_lower = type(classifier).__name__.lower()

            if 'tree' in model_name_lower or 'forest' in model_name_lower or 'gradient' in model_name_lower:
                # Tree-based models (including gradient boosting)
                try:
                    # Transform data through pipeline preprocessing steps
                    X_transformed = pipeline[:-1].transform(X_sample_array)
                    explainer = shap.TreeExplainer(classifier)
                    shap_values = explainer.shap_values(X_transformed)
                except Exception as e:
                    print(f"    TreeExplainer failed ({e}), falling back to KernelExplainer...")
                    # Fallback to KernelExplainer for problematic tree models
                    def predict_wrapper(X):
                        return pipeline.predict_proba(X) if hasattr(pipeline, 'predict_proba') else pipeline.predict(X)

                    background = X_sample_array[:min(50, len(X_sample_array))]
                    explainer = shap.KernelExplainer(predict_wrapper, background)
                    shap_values = explainer.shap_values(X_sample_array[:min(100, len(X_sample_array))])
            elif 'linear' in model_name_lower or 'logistic' in model_name_lower:
                # Linear models
                try:
                    explainer = shap.LinearExplainer(classifier, pipeline[:-1].transform(X_sample_array[:50]))
                    X_transformed = pipeline[:-1].transform(X_sample_array)
                    shap_values = explainer.shap_values(X_transformed)
                except Exception as e:
                    print(f"    LinearExplainer failed ({e}), falling back to KernelExplainer...")
                    # Fallback to KernelExplainer
                    def predict_wrapper(X):
                        return pipeline.predict_proba(X) if hasattr(pipeline, 'predict_proba') else pipeline.predict(X)

                    background = X_sample_array[:min(50, len(X_sample_array))]
                    explainer = shap.KernelExplainer(predict_wrapper, background)
                    shap_values = explainer.shap_values(X_sample_array[:min(100, len(X_sample_array))])
            else:
                # General case - use KernelExplainer with full pipeline
                # Create a wrapper function to avoid SHAP trying to set feature_names_in_
                def predict_wrapper(X):
                    return pipeline.predict_proba(X) if hasattr(pipeline, 'predict_proba') else pipeline.predict(X)

                background = X_sample_array[:min(50, len(X_sample_array))]
                explainer = shap.KernelExplainer(predict_wrapper, background)
                shap_values = explainer.shap_values(X_sample_array[:min(100, len(X_sample_array))])

            # Handle multi-class output
            if isinstance(shap_values, list):
                # Multi-class: convert to 3D array
                shap_values = np.array(shap_values).transpose(1, 2, 0)
            elif shap_values.ndim == 2:
                # Binary classification or single output
                pass

            # Use the transformed data for visualisations if available
            if 'X_transformed' in locals():
                X_for_viz = X_transformed
                # Update feature names to match transformed features if needed
                if feature_names is not None and len(feature_names) != X_transformed.shape[1]:
                    feature_names = [f"feat_{i}" for i in range(X_transformed.shape[1])]
            else:
                X_for_viz = X_sample_array

            # Generate visualisations and reports
            _generate_shap_visualisations(
                shap_values, X_for_viz, feature_names, classifier,
                model_name, subdirs, run_id
            )

            print(f"SHAP analysis complete for {model_name}")

        except Exception as e:
            print(f"SHAP analysis failed for {model_name}: {e}")

    except ImportError:
        print(f"SHAP not available for {model_name}")
    except Exception as e:
        print(f"Comparative SHAP failed for {model_name}: {e}")


def _create_comparative_shap_summary(top_models, subdirs, run_id):
    """Create a summary document comparing SHAP results across models."""
    try:
        import json
        from datetime import datetime, timezone

        # Validate that referenced files actually exist
        existing_reports = []
        existing_visualisations = {}

        for model_name, score in top_models:
            model_key = model_name.replace(' ', '_').lower()

            # Check for analysis report
            report_file = f"shap_analysis_report_{model_key}_{run_id}.json"
            report_path = subdirs["meta"] / report_file
            if report_path.exists():
                existing_reports.append(report_file)

            # Check for visualisations
            viz_files = []
            potential_viz = [
                f"shap_summary_{model_key}_{run_id}.png",
                f"shap_bar_{model_key}_{run_id}.png",
                f"shap_waterfall_{model_key}_sample1_{run_id}.png",
                f"shap_waterfall_{model_key}_sample2_{run_id}.png",
                f"shap_waterfall_{model_key}_sample3_{run_id}.png",
                f"shap_dependence_{model_key}_feat1_{run_id}.png",
                f"shap_dependence_{model_key}_feat2_{run_id}.png"
            ]

            for viz_file in potential_viz:
                viz_path = subdirs["figures"] / viz_file
                if viz_path.exists():
                    viz_files.append(viz_file)

            if viz_files:  # Only include models that have visualisations
                existing_visualisations[model_name] = viz_files

        # Only create summary if we have actual files
        if not existing_reports and not existing_visualisations:
            print("  No SHAP files found - skipping comparative summary")
            return

        summary = {
            "metadata": {
                "analysis_type": "comparative_shap",
                "run_id": run_id,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "models_analyzed": [{"name": name, "cv_score": score} for name, score in top_models],
                "files_validated": True,
                "reports_found": len(existing_reports),
                "models_with_visualisations": len(existing_visualisations)
            },
            "comparison_notes": {
                "purpose": "Compare feature importance patterns across top-performing models",
                "methodology": "SHAP (SHapley Additive exPlanations) analysis on test set samples",
                "interpretation": "Higher |SHAP| values indicate greater feature importance for predictions",
                "note": "Only models with successfully generated SHAP files are included"
            },
            "model_reports": existing_reports,
            "visualisations": existing_visualisations
        }

        summary_path = subdirs["meta"] / f"comparative_shap_summary_{run_id}.json"
        with summary_path.open("w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2)

        print(f"  Comparative SHAP summary saved -> {summary_path}")
        print(f"    • Reports found: {len(existing_reports)}")
        print(f"    • Models with visualisations: {len(existing_visualisations)}")

    except Exception as e:
        print(f"  Failed to create comparative SHAP summary: {e}")


def analyse_best_model_features(pipeline, model_name="Model", feature_names=None):
    """Analyse feature importance for the best model if it supports feature analysis.

    Args:
        pipeline: Trained pipeline with the best model
        model_name: Name of the model for display purposes
        feature_names: Optional feature names, will be inferred if not provided

    Returns:
        Feature importance scores if available, None otherwise
    """
    try:
        classifier = pipeline.named_steps["classifier"]

        # Check if model supports feature importance analysis
        has_feature_importances = hasattr(classifier, 'feature_importances_')
        has_coef = hasattr(classifier, 'coef_')

        if not (has_feature_importances or has_coef):
            print(f"\n=== {model_name.upper()} FEATURE ANALYSIS ===")
            print(f"  {model_name} does not support feature importance analysis")
            print(f"  (No feature_importances_ or coef_ attributes)")
            return None

        print(f"\n=== {model_name.upper()} FEATURE ANALYSIS ===")

        # Get feature names from the pipeline - avoid accessing feature_names_in_ directly
        if feature_names is None:
            try:
                # Try to get feature names from the vectorizer step
                vectorizer = getattr(pipeline, "named_steps", {}).get("tfidf")
                if vectorizer is not None and hasattr(vectorizer, "get_feature_names_out"):
                    feature_names = vectorizer.get_feature_names_out()
            except (AttributeError, TypeError):
                pass

            if feature_names is None:
                # Fallback: generate generic feature names based on available features
                if has_feature_importances:
                    n_features = len(classifier.feature_importances_)
                elif has_coef:
                    n_features = classifier.coef_.shape[1] if classifier.coef_.ndim > 1 else len(classifier.coef_)
                else:
                    n_features = 0
                feature_names = [f"feat_{i}" for i in range(n_features)]

        # Get feature importance scores
        if has_feature_importances:
            importance_scores = classifier.feature_importances_
            importance_type = "Feature Importances"
        elif has_coef:
            # For linear models, use absolute coefficients as importance
            coef = classifier.coef_
            if coef.ndim > 1:
                # Multi-class: use mean absolute coefficients across classes
                importance_scores = np.mean(np.abs(coef), axis=0)
            else:
                # Binary classification: use absolute coefficients
                importance_scores = np.abs(coef)
            importance_type = "Coefficient Magnitudes"
        else:
            return None

        # Get top 15 most important features
        top_indices = np.argsort(importance_scores)[-15:][::-1]

        print(f"Top 15 Most Important Features ({importance_type}):")
        for i, idx in enumerate(top_indices):
            feature_name = feature_names[idx] if idx < len(feature_names) else f"feat_{idx}"
            importance = importance_scores[idx]
            print(f"  {i+1:2d}. {feature_name[:50]:<50} {importance:.4f}")

        return importance_scores

    except Exception as e:
        print(f"\n=== {model_name.upper()} FEATURE ANALYSIS ===")
        print(f"  Error analyzing features: {e}")
        return None


def analyse_model_predictions_detailed(trained_pipeline):
    """Analyse model predictions in detail with confidence scores."""
    # Import here to avoid circular imports
    from classifier_lib.data import create_enhanced_test_cases, create_feature_text
    from classifier_lib.preprocessing import _convert_features_to_dataframe
    import pandas as pd

    print("\n=== DETAILED PREDICTION ANALYSIS ===")

    test_cases = create_enhanced_test_cases()
    test_features_dicts = [create_feature_text(case) for case in test_cases]

    # Convert to DataFrame format using the same function as training
    test_cases_df = pd.DataFrame(test_cases)
    test_features_series = pd.Series(test_features_dicts)

    # Use the same conversion function as training to get proper feature names
    test_df = _convert_features_to_dataframe(test_cases_df, test_features_series)

    # Extract only the feature columns (those starting with 'feat_')
    feature_cols = [col for col in test_df.columns if col.startswith('feat_')]
    test_features_only = test_df[feature_cols]

    # Ensure all expected feature columns are present
    expected_features = getattr(trained_pipeline, '_feature_names_in', None)
    if expected_features is not None:
        for col in expected_features:
            if col not in test_features_only.columns:
                test_features_only[col] = 0
        test_features_only = test_features_only[expected_features]

    predictions = trained_pipeline.predict(test_features_only)
    probabilities = trained_pipeline.predict_proba(test_features_only)
    classes = trained_pipeline.classes_

    for i, (case, prediction, proba) in enumerate(
        zip(test_cases, predictions, probabilities)
    ):
        print(f"\nTest Case {i+1}:")
        print(f"Code: {case['code'][:50]}{'...' if len(case['code']) > 50 else ''}")
        print(f"Expected Error Type: {case['error_description']}")
        print(f"Predicted Error Type: {prediction}")

        # Show confidence scores
        max_prob = max(proba)
        print(f"Confidence: {max_prob:.3f}")

        # Show top 3 predictions with probabilities
        top_3_indices = np.argsort(proba)[-3:][::-1]
        print("Top 3 predictions:")
        for j, idx in enumerate(top_3_indices):
            print(f"  {j+1}. {classes[idx]}: {proba[idx]:.3f}")

        # Show feature summary for debugging
        feature_summary = {k: v for k, v in test_features_dicts[i].items() if v != 0}
        print(f"Non-zero features: {list(feature_summary.keys())[:5]}...")

        # Determine if prediction is reasonable
        if prediction == "NoError" and case["ast_message"]:
            print("⚠️  WARNING: Predicted NoError for code with known issues")
        elif prediction != "NoError" and not case["ast_message"]:
            print("ℹ️  INFO: Predicted error for seemingly correct code")


# def analyse_complexity_difference(correct_code, buggy_code):
#     """Analyse complexity metrics differences between correct and buggy code"""
#     features = []

#     def get_metrics(code):
#         try:
#             tree = ast.parse(code)
#             metrics = {
#                 "nodes": len(list(ast.walk(tree))),
#                 "functions": len(
#                     [n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]
#                 ),
#                 "classes": len(
#                     [n for n in ast.walk(tree) if isinstance(n, ast.ClassDef)]
#                 ),
#                 "loops": len(
#                     [n for n in ast.walk(tree) if isinstance(n, (ast.For, ast.While))]
#                 ),
#                 "conditionals": len(
#                     [n for n in ast.walk(tree) if isinstance(n, ast.If)]
#                 ),
#                 "calls": len([n for n in ast.walk(tree) if isinstance(n, ast.Call)]),
#             }
#             return metrics
#         except (SyntaxError, ValueError, TypeError):
#             return {
#                 "nodes": 0,
#                 "functions": 0,
#                 "classes": 0,
#                 "loops": 0,
#                 "conditionals": 0,
#                 "calls": 0,
#             }

#     correct_metrics = get_metrics(correct_code)
#     buggy_metrics = get_metrics(buggy_code)

#     for metric, correct_val in correct_metrics.items():
#         buggy_val = buggy_metrics[metric]
#         if buggy_val > correct_val:
#             features.append(f"increased_{metric}")
#         elif buggy_val < correct_val:
#             features.append(f"decreased_{metric}")

#     return features


def group_and_analyse_errors(df):
    """Group similar error types and analyse the grouping effectiveness."""
    print("Grouping similar error types into broader categories...")
    df = group_error_types(df)

    # Show the final grouped distribution
    print("\n=== FINAL GROUPED ERROR DISTRIBUTION ===")
    grouped_error_counts = df["grouped_error_type"].value_counts()
    print("After grouping:")
    for error_type, count in grouped_error_counts.items():
        print(f"  • {error_type}: {count} samples")

    print("\nGrouping effectiveness:")
    print(f"  • Original classes: {df['intended_error_type'].nunique()}")
    print(f"  • Grouped classes: {df['grouped_error_type'].nunique()}")
    print(
        f"  • Reduction: {df['intended_error_type'].nunique() - df['grouped_error_type'].nunique()} classes"
    )
    print(
        f"  • Average samples per grouped class: {len(df) / df['grouped_error_type'].nunique():.1f}"
    )

    return df
