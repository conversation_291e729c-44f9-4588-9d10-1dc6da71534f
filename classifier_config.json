{"tuning": {"enabled": true, "top_n": 3, "random_search_iters": 4, "cv_folds": 3, "exclusions": ["xgboost"], "n_jobs": -1}, "explainability": {"use_shap": true, "shap_sample_size": 150, "permutation_importance_sample": 300}, "evaluation": {"bootstrap_iterations": 200, "bootstrap_random_state": 1, "calibration_bins": 10, "calibration_posthoc": {"enabled": true, "method": "isotonic", "cv": 5, "allow_in_nested": false, "n_jobs": null}, "pr_curves": {"plot": true}}}