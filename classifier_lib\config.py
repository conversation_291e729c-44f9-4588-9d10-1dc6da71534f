from __future__ import annotations
import json
import argparse
import os
from pathlib import Path
from typing import Any, Dict
from datetime import datetime, timezone


argparser = argparse.ArgumentParser(description="Error Type Classifier")

argparser.add_argument(
    "--dataset",
    type=str,
    default="datasets/single_code_errors.csv",
    help="Path to dataset CSV",
)
argparser.add_argument(
    "--sample-size-limit",
    type=int,
    default=None,
    help="Limit the number of samples to this value",
)
argparser.add_argument(
    "--config", type=str, default=None, help="Path to classifier_config.json"
)
# Toggles for ablations and features
argparser.add_argument(
    "--no-tuning", action="store_true", help="Disable hyperparameter tuning"
)
argparser.add_argument(
    "--no-shap", action="store_true", help="Disable SHAP explainability"
)
argparser.add_argument(
    "--shap-sample", type=int, default=None, help="Override SHAP sample size"
)
argparser.add_argument(
    "--no-grouping",
    action="store_true",
    help="Ablation: disable error-type grouping (use original intended_error_type)",
)
argparser.add_argument(
    "--no-balance",
    action="store_true",
    help="Ablation: disable NoError augmentation (errors only)",
)
# allow forcing/skip of final test evaluation irrespective of CV validity
argparser.add_argument(
    "--do-final-test",
    choices=["auto", "true", "false"],
    default="auto",
    help="Control final test evaluation: auto (default), true, false",
)
# global seed override
argparser.add_argument(
    "--seed",
    type=int,
    default=None,
    help="Override global random_state (applies across models, CV, and splits)",
)

# Calibration toggles
argparser.add_argument(
    "--calibration",
    choices=["off", "isotonic", "sigmoid"],
    default=None,
    help="Post-hoc calibration method override: off|isotonic|sigmoid (default: use config)",
)
argparser.add_argument(
    "--calibration-cv",
    type=int,
    default=None,
    help="Override calibration CV folds (post-hoc calibration)",
)
argparser.add_argument(
    "--calibration-njobs",
    type=int,
    default=None,
    help="Override calibration parallel jobs if supported by sklearn version",
)
argparser.add_argument(
    "--calibration-bins",
    type=int,
    default=None,
    help="Override number of bins for reliability/ECE computation",
)

name_to_type = {
    "Logistic Regression": "logistic_regression",
    "Random Forest": "random_forest",
    "SVM (RBF)": "svm_rbf",
    "SVM (Linear)": "svm_linear",
    "Naive Bayes": "naive_bayes",
    "Neural Network": "neural_network",
    "Gradient Boosting": "gradient_boosting",
    "K-Nearest Neighbors": "knn",
    "Decision Tree": "decision_tree",
    "XGBoost": "xgboost",
    "HistGradient Boosting": "hist_gradient_boosting",
}


DEFAULT_CONFIG: Dict[str, Any] = {
    # Global controls
    "general": {
        # Single source of truth for randomness across the project
        "random_state": 1,
        # If True, set process-level seeds (PYTHONHASHSEED, random, numpy)
        "set_process_seeds": True,
        "sample_size_limit": None,  # for faster testing; None to disable
    },
    "tuning": {
        "enabled": True,
        "top_n": 3,
        "random_search_iters": 4,
        "cv_folds": 3,
        "exclusions": [],
        "n_jobs": -1,
    },
    # Model selection and tuning candidate ranking controls
    "selection": {
        # metric: "cv" (pure CV accuracy) or "cv_time" (CV-time tradeoff)
        "metric": "cv_time",
        # Penalty weight applied to (normalised) time; higher => prefer faster models
        "time_weight": 0.01,
        # Normalise training time by: "min" (fastest model) or "median"
        "time_norm": "min",
        # Use logarithmic penalty on relative time (stable under large time ranges)
        "use_log": True,
        # Number of models to consider for hyperparameter refinement (overrides tuning.top_n if provided)
        "top_n_for_tuning": 3,
        # Optional stability penalty on high CV variance
        "stability": {
            "std_threshold": 0.12,  # no penalty below this CV std
            "penalty_weight": 0.5,  # linear weight on (std - threshold)
            "mode": "linear",  # linear|quadratic
            "gate": True,  # if True, exclude models with std > max_std
            "max_std": 0.25,
        },
        # Optional hard gate to drop models slower than multiple of base time (0 disables)
        "time_gate_multiple": 0.0,
    },
    # Class balance controls (weights and optional resampling)
    "class_balance": {
        # Whether to pass class_weight to estimators that support it
        "use_class_weight": True,
        # Can be "balanced", null, or a dict mapping class label -> weight
        "class_weight": "balanced",
        # Optional resampling applied ONLY on training folds via pipeline
        "resampling": {
            "enabled": True,
            # One of: "random_over" | "smote" | "random_under"
            "method": "random_over",
            # Strategy passed to imblearn sampler; "auto" uses default per method
            # Can also be a float (for under/over) or a dict {class_label: target_n}
            "strategy": "auto",
            # When None, falls back to general.random_state
            "random_state": 1,
        },
        # Print class distribution tables before/after balancing and split
        "report_distributions": True,
    },
    "explainability": {
        "use_shap": True,
        "shap_sample_size": 150,
        "permutation_importance_sample": 300,
        # SHAP control knobs
        "shap_background_size": 50,
        "shap_max_evals_cap": 5000,
        "shap_skip_if_costly": True,
        # Only run SHAP for these model tags (see analysis.py for tag mapping)
        "shap_model_allowlist": ["linear", "random_forest", "xgboost"],
        # Export top-N tokens by mean |SHAP|
        "shap_top_n_tokens": 20,
    },
    "evaluation": {
        "bootstrap_iterations": 1000,
        # When None, fall back to general.random_state
        "bootstrap_random_state": 1,
        "calibration_bins": 10,
        # CV splitter seeding behaviour: "global" uses general.random_state; "none" leaves unseeded; "fixed" uses this block's explicit cv_random_state if provided
        "cv": {
            "split_random_state_mode": "global",  # global|none|fixed
            # Used only when mode == "fixed"
            "cv_random_state": 1,
            # New: CV robustness controls
            "n_splits": 3,
            "repeats": 2,  # set >1 to enable RepeatedStratifiedKFold
            "scoring": "accuracy",  # accuracy|balanced_accuracy|macro_f1
            "warn_std_threshold": 0.15,
        },
        # Seed sensitivity study across multiple runs
        "seed_sensitivity": {
            "enabled": False,
            # If explicit seeds omitted, will generate seeds from base_seed and count
            "seeds": [25, 101, 202, 303, 404],
            # If using generated seeds
            "count": 2,
            "base_seed": 25,
            "offset": 100,
            # Skip final test during study to avoid peeking
            "skip_final_test": False,
        },
        # Nested CV controls for robust performance estimation
        "nested_cv": {
            "enabled": True,
            "outer_splits": 5,
            "outer_repeats": 1,
            # When None, fall back to general.random_state
            "random_state": 1,
        },
        # Learning curve controls
        "learning_curve": {
            "enabled": True,
            "scoring": "balanced_accuracy",  # accuracy|balanced_accuracy|macro_f1
            "train_sizes": [i/20 for i in range(1, 21)],
            # When None, fall back to general.random_state
            "random_state": 1,
            # Save CSV alongside PNG
            "save_csv": True,
        },
        # Paired significance testing between top models
        "significance": {
            "enabled": True,
            "top_k": 2,
            # one of: "mcnemar" | "ttest" | "both"
            "methods": "both",
        },
        # Optional post-hoc probability calibration of the selected best model
        "calibration_posthoc": {
            "enabled": True,
            # one of: "isotonic" | "sigmoid"
            "method": "isotonic",
            # Number of CV folds to use internally for calibration
            "cv": 3,
            # Allow running inside nested CV inner loops
            "allow_in_nested": False,
            # Optional override for parallelism; when None, fall back to tuning.n_jobs logic
            "n_jobs": None,
        },
        # Plotting controls for per-class PR curves of the best model
        "pr_curves": {"plot": True},
    },
    # Feature extraction controls
    "features": {
        # Default to word analyser; switch via config to compare char_wb.
        "tfidf_analyzer": "word",
        # Only used when tfidf_analyzer == "char_wb"
        "char_ngram_range": [3, 5],
    },
}


def get_config(config_path: str | None = None) -> Dict[str, Any]:
    """Load configuration from classifier_config.json if present, else defaults.

    Search order:
    1) Provided config_path
    2) Project root (same dir as this file's parent)
    3) Current working directory
    """
    candidates = []
    if config_path:
        candidates.append(Path(config_path))
    # project root (code folder)
    candidates.append(Path(__file__).resolve().parent.parent / "classifier_config.json")
    # working dir
    candidates.append(Path("classifier_config.json").resolve())

    for p in candidates:
        try:
            if p.exists():
                with p.open("r", encoding="utf-8") as f:
                    data = json.load(f)
                # shallow-merge onto defaults
                cfg = DEFAULT_CONFIG.copy()
                for k, v in (data or {}).items():
                    if isinstance(v, dict) and isinstance(cfg.get(k), dict):
                        cfg[k] = {**cfg[k], **v}
                    else:
                        cfg[k] = v
                return cfg
        except (OSError, json.JSONDecodeError, ValueError):
            # Fall through to defaults on any error
            break
    return DEFAULT_CONFIG.copy()


def create_run():
    # Generate consistent run_id for this entire training run
    run_id = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    run_dir = get_run_output_dir(run_id)
    subdirs = create_run_subdirs(run_dir)
    return run_id, run_dir, subdirs


def get_run_output_dir(run_id: str | None = None, base_dir: str = "artefacts") -> Path:
    """
    Generate a consistent output directory structure for a run.

    Args:
        run_id: Unique run identifier. If None, generates timestamp-based ID.
        base_dir: Base directory name (default: "artefacts")

    Returns:
        Path to the run-specific directory
    """
    if run_id is None:
        run_id = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")

    base_path = Path(base_dir)
    run_dir = base_path / f"run_{run_id}"
    return run_dir


def create_run_subdirs(run_dir: Path) -> Dict[str, Path]:
    """
    Create standard subdirectories for a run and return paths.

    Args:
        run_dir: Run-specific directory path

    Returns:
        Dictionary with subdirectory paths: models, metrics, figures, tables, meta
    """
    subdirs = {
        "models": run_dir / "models",
        "metrics": run_dir / "metrics",
        "figures": run_dir / "figures",
        "tables": run_dir / "tables",
        "meta": run_dir / "meta",
    }

    # Create the run directory and all subdirectories
    run_dir.mkdir(parents=True, exist_ok=True)
    for subdir in subdirs.values():
        subdir.mkdir(parents=True, exist_ok=True)

    return subdirs


def parse_config():
    args = argparser.parse_args()
    cfg = get_config(args.config)
    if args.no_tuning:
        cfg["tuning"]["enabled"] = False
    if args.no_shap:
        cfg["explainability"]["use_shap"] = False
    if args.shap_sample is not None:
        cfg["explainability"]["shap_sample_size"] = int(args.shap_sample)
    if args.sample_size_limit is not None:
        cfg["general"]["sample_size_limit"] = int(args.sample_size_limit)
    # Seed override
    if args.seed is not None:
        cfg.setdefault("general", {})["random_state"] = int(args.seed)
    # Propagate final test control to config for train_model to honour
    if args.do_final_test != "auto":
        cfg.setdefault("evaluation", {})["do_final_test"] = (
            args.do_final_test.lower() == "true"
        )
    # Record ablation settings into config for provenance
    cfg.setdefault("experiment", {}).update(
        {
            "grouping": (not args.no_grouping),
            "include_no_error": (not args.no_balance),
            "label_space": ("grouped" if not args.no_grouping else "original"),
            "balance_mode": (
                "errors+noerror" if not args.no_balance else "errors-only"
            ),
        }
    )
    # Apply calibration overrides if provided
    if args.calibration is not None:
        cal_cfg = cfg.setdefault("evaluation", {}).setdefault("calibration_posthoc", {})
        if args.calibration == "off":
            cal_cfg["enabled"] = False
        else:
            cal_cfg["enabled"] = True
            cal_cfg["method"] = args.calibration
    if args.calibration_cv is not None:
        cfg.setdefault("evaluation", {}).setdefault("calibration_posthoc", {})["cv"] = (
            int(args.calibration_cv)
        )
    if args.calibration_njobs is not None:
        cfg.setdefault("evaluation", {}).setdefault("calibration_posthoc", {})[
            "n_jobs"
        ] = int(args.calibration_njobs)
    if args.calibration_bins is not None:
        cfg.setdefault("evaluation", {})["calibration_bins"] = int(
            args.calibration_bins
        )
    return cfg, args


def get_effective_n_jobs(cfg: dict | None = None, req_n_jobs: int | None = None) -> int:
    """
    Get effective n_jobs value with consistent min-max logic applied across the application.

    Args:
        cfg: Configuration dictionary
        req_n_jobs: Requested n_jobs value (overrides config if provided)

    Returns:
        Effective n_jobs value with min-max constraints applied
    """
    if cfg is None:
        cfg = get_config(None)

    # Get requested n_jobs from parameter or config
    if req_n_jobs is not None:
        requested = req_n_jobs
    else:
        try:
            requested = int(cfg.get("tuning", {}).get("n_jobs", -1))
        except (TypeError, ValueError, AttributeError):
            requested = -1

    # Get CPU count
    cpu = max(1, (os.cpu_count() or 2))

    # Apply min-max logic consistently
    if requested == -1:
        # Use up to 75% of cores but at least 1 and at most 24
        effective = int(max(1, min(24, cpu // 1.5)))
    else:
        # Respect requested value but cap it to available cores minus 1
        effective = int(max(1, min(requested, max(1, cpu - 1))))

    return effective
