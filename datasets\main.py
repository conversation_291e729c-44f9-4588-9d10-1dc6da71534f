"""Program to generate buggy code from correct code using an error injection pipeline"""

import argparse
import csv
import random
import pandas as pd
from error_injection_pipeline import multi_inject_errors
from ast_and_lint import analyse_code


# parse console arguments
INPUT = "Python Programming Questions Dataset.csv"
OUTPUT = "single_code_errors.csv"
SAMPLE_SIZE = None
MULTI_INJECT = 1

parser = argparse.ArgumentParser(
    description="Generate buggy code from correct code snippets."
)
parser.add_argument(
    "--input", type=str, default=INPUT, help="Path to the input CSV file."
)
parser.add_argument(
    "--output", type=str, default=OUTPUT, help="Path to the output CSV file."
)
parser.add_argument(
    "--sample_size", type=int, default=SAMPLE_SIZE, help="Number of samples to process."
)
parser.add_argument(
    "--multi_inject",
    type=int,
    default=MULTI_INJECT,
    help="Number of error injection passes.",
)
args = parser.parse_args()

INPUT = args.input
OUTPUT = args.output
SAMPLE_SIZE = args.sample_size
MULTI_INJECT = args.multi_inject
SEED = 1
df = pd.read_csv(INPUT)
if SAMPLE_SIZE is None or SAMPLE_SIZE > len(df):
    SAMPLE_SIZE = len(df)
# create a sample of correct code snippets
correct_snippets = df["Output"].sample(n=SAMPLE_SIZE, random_state=SEED).tolist()

total = len(correct_snippets)
WRITTEN = 0
SKIPPED = 0
PROGRESS_EVERY = max(1, total // 100)

fieldnames = [
    "correct_code",
    "buggy_code",
    "intended_error_type",
    "ast_message",
    "error_description",
]
with open(OUTPUT, "w", encoding="utf-8", newline="") as f_out:
    writer = csv.DictWriter(f_out, fieldnames=fieldnames)
    writer.writeheader()
    for idx, original in enumerate(correct_snippets, start=1):
        final_code, metas = multi_inject_errors(
            original, passes=random.randint(1, MULTI_INJECT)
        )
        # Collect intended error types (filter out None)
        intended = [m.get("error_type") for m in metas if m and m.get("error_type")]
        if not intended:  # no successful injection
            SKIPPED += 1
        else:
            try:
                analysis = analyse_code(final_code)
            except UnicodeEncodeError:
                analysis = {
                    "error_type": "UnicodeEncodeError",
                    "error_description": "Failed due to UnicodeEncodeError",
                }
            ast_msg = analysis.get("error_type") or "NoError"
            err_desc = analysis.get("error_description") or "No description"
            writer.writerow(
                {
                    fieldnames[0]: original,
                    fieldnames[1]: final_code,
                    fieldnames[2]: intended[0],  # take the first intended error
                    fieldnames[3]: ast_msg,
                    fieldnames[4]: err_desc,
                }
            )
            WRITTEN += 1
        if idx % PROGRESS_EVERY == 0:
            pct = (idx / total) * 100
            print(
                f"Progress: {idx}/{total} ({pct:.1f}%) | written={WRITTEN} skipped={SKIPPED}"
            )

print(
    f"Completed. Total input: {total}, written: {WRITTEN}, skipped: {SKIPPED}, output: {OUTPUT}"
)
