""" Output and visualisation utilities for the classifiers """

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix
from sklearn.model_selection import StratifiedShuffleSplit
from sklearn.base import clone
from sklearn.metrics import accuracy_score, balanced_accuracy_score, f1_score

def plot_confusion_matrix(
    y_true, y_pred, class_names, title="Confusion Matrix", figsize=(10, 8),
    subdirs: dict | None = None, 
    run_id: str | None = None
):
    """
    Creates and displays a confusion matrix with proper formatting.
    Returns the saved image path.
    """
    cm = confusion_matrix(y_true, y_pred, labels=class_names)

    # Create the plot
    plt.figure(figsize=figsize)

    # Use percentage and count annotations
    cm_percent = cm.astype("float") / cm.sum(axis=1)[:, np.newaxis] * 100

    # Create heatmap
    sns.heatmap(
        cm_percent,
        annot=True,
        fmt=".2f",
        cmap="Blues",
        xticklabels=class_names,
        yticklabels=class_names,
        cbar_kws={"label": "Count"},
    )

    plt.title(title)
    plt.xlabel("Predicted Error Type")
    plt.ylabel("Actual Error Type")
    plt.xticks(rotation=45, ha="right")
    plt.yticks(rotation=0)
    plt.tight_layout()

    # Save the confusion matrix
    filename = f'{title.lower().replace(" ", "_")}{run_id}.png'
    if subdirs and 'figures' in subdirs:
        out_path = subdirs['figures'] / filename
    else:
        out_path = filename
    plt.savefig(
        out_path,
        dpi=300,
        bbox_inches="tight",
    )
    plt.close()
    print(f"Saved confusion matrix -> {out_path}")

    # Print detailed statistics
    print(f"\n{title} - Detailed Analysis:")
    print("-" * 50)

    for i, class_name in enumerate(class_names):
        if cm[i].sum() > 0:  # Avoid division by zero
            precision = cm[i, i] / cm[:, i].sum() if cm[:, i].sum() > 0 else 0
            recall = cm[i, i] / cm[i].sum()
            print(f"{class_name}:")
            print(f"- True instances: {cm[i].sum()}")
            print(f"- Correctly predicted: {cm[i, i]}")
            print(f"- Precision: {precision:.3f}")
            print(f"- Recall: {recall:.3f}")

    return str(out_path)


def plot_reliability_curve(
    frac_pos, mean_pred, title="Reliability Curve", figsize=(6, 5), subdirs: dict | None = None
):
    """Plot and save a reliability (calibration) curve and return the saved path.

    Args:
        frac_pos: list or array of fraction of positives per bin (y-axis)
        mean_pred: list or array of mean predicted probability per bin (x-axis)
        title: plot title
        figsize: figure size
    """
    try:
        plt.figure(figsize=figsize)
        # Perfect calibration line
        plt.plot([0, 1], [0, 1], linestyle="--", color="gray", label="Perfect")
        # Empirical calibration
        plt.plot(mean_pred, frac_pos, marker="o", label="Model")
        plt.xlabel("Mean predicted probability")
        plt.ylabel("Fraction of positives")
        plt.title(title)
        plt.legend()
        plt.tight_layout()
        filename = f"calibration_{title.lower().replace(' ', '_')}.png"
        out_path = subdirs['figures'] / filename if subdirs else filename
        plt.savefig(out_path, dpi=300, bbox_inches="tight")
        # plt.show()
        print(f"Saved calibration plot -> {out_path}")
        return out_path
    except Exception as e: #pylint: disable=broad-except
        print(f"WARNING: Failed to plot reliability curve: {e}")
        return None


def plot_reliability_curve_comparison(
    frac_pos_pre,
    mean_pred_pre,
    frac_pos_post,
    mean_pred_post,
    title: str = "Reliability Curve (Pre vs Post Calibration)",
    figsize=(6, 5),
    subdirs=None,
):
    """Plot pre- vs post-calibration reliability curves on one figure and return the saved path.

    Args:
        frac_pos_pre, mean_pred_pre: pre-calibration series
        frac_pos_post, mean_pred_post: post-calibration series
        title: plot title
    """
    try:
        plt.figure(figsize=figsize)
        # Perfect calibration
        plt.plot([0, 1], [0, 1], linestyle="--", color="gray", label="Perfect")
        # Pre
        plt.plot(mean_pred_pre, frac_pos_pre, marker="o", label="Pre-calibration")
        # Post
        plt.plot(mean_pred_post, frac_pos_post, marker="s", label="Post-calibration")
        plt.xlabel("Mean predicted probability")
        plt.ylabel("Fraction of positives")
        plt.title(title)
        plt.legend()
        plt.tight_layout()
        filename = f"calibration_{title.lower().replace(' ', '_')}.png"
        out_path = subdirs['figures'] / filename if subdirs else filename
        plt.savefig(out_path, dpi=300, bbox_inches="tight")
        # plt.show()
        print(f"Saved calibration comparison plot -> {out_path}")
        return out_path
    except Exception as e: #pylint: disable=broad-except
        print(f"WARNING: Failed to plot calibration comparison: {e}")
        return None


def print_dataset_summary(df):
    """Print summary information about the dataset status."""
    print(f"\n{'='*60}")
    print("DATASET STATUS SUMMARY:")
    print(f"Current size: {len(df)} samples")


def plot_learning_curve_pipeline(
    pipeline,
    X_train,
    y_train,
    X_test,
    y_test,
    *,
    train_sizes=None,
    scoring: str = "accuracy",
    title: str | None = None,
    figsize=(7, 5),
    random_state: int = 25,
    subdirs: dict | None = None,
) -> None:
    """Compute and save a simple learning curve: train/test score vs training size.

    This refits a fresh clone of the provided pipeline for each training size on a
    stratified subsample of the training data, then scores on the subsample (train)
    and the held-out test set (test).

    Args:
        pipeline: fitted Pipeline to clone (hyperparameters reused)
        X_train, y_train: training data used for fitting subsets
        X_test, y_test: held-out test set used for the test curve
        train_sizes: list of fractions (0-1] or absolute integers; default fractions
        scoring: 'accuracy' (default), 'balanced_accuracy', or 'macro_f1'
        title: optional plot title
        figsize: figure size
        random_state: for reproducible subsampling
    """
    try:
       

        # Prepare sizes
        n_train = len(y_train)
        if train_sizes is None:
            train_sizes = [0.1, 0.2, 0.4, 0.6, 0.8, 1.0]
        # Convert to absolute counts
        abs_sizes = []
        for s in train_sizes:
            if isinstance(s, float):
                abs_sizes.append(max(2, int(np.ceil(s * n_train))))
            else:
                abs_sizes.append(int(s))
        # Deduplicate and clip
        abs_sizes = sorted(set(min(max(2, s), n_train) for s in abs_sizes))

        # Scorer
        def _score(y_true, y_pred):
            match scoring:
                case "balanced_accuracy":
                    return balanced_accuracy_score(y_true, y_pred)
                case "macro_f1":
                    return f1_score(y_true, y_pred, average="macro", zero_division=0)
                case _:
                    return accuracy_score(y_true, y_pred)

        train_scores = []
        test_scores = []
        effective_sizes = []

        splitter = StratifiedShuffleSplit(n_splits=1, random_state=random_state)

        # We will sample a new stratified subset for each size to avoid class-drop issues
        for m in abs_sizes:
            # Some splits may fail if a class occurrence < 1 in the subset; retry with smaller test_size logic
            # Use train_size=m on full training set
            try:
                sss = StratifiedShuffleSplit(
                    n_splits=1, train_size=m, random_state=random_state
                )
                idx_train, _ = next(sss.split(np.arange(n_train), y_train))
            except Exception:
                # Fallback: take the first m indices
                idx_train = np.arange(m)
            # Build subset
            X_sub = (
                X_train.iloc[idx_train]
                if hasattr(X_train, "iloc")
                else np.array(X_train)[idx_train]
            )
            y_sub = (
                y_train.iloc[idx_train]
                if hasattr(y_train, "iloc")
                else np.array(y_train)[idx_train]
            )

            # Fit clone
            model = clone(pipeline)
            model.fit(X_sub, y_sub)

            # Train score (on subset)
            y_sub_pred = model.predict(X_sub)
            tr = _score(y_sub, y_sub_pred)

            # Test score (on held-out test set)
            y_te_pred = model.predict(X_test)
            te = _score(y_test, y_te_pred)

            train_scores.append(tr)
            test_scores.append(te)
            effective_sizes.append(len(y_sub))

        # Plot
        import matplotlib.pyplot as _plt

        _plt.figure(figsize=figsize)
        _plt.plot(effective_sizes, train_scores, marker="o", label="Train")
        _plt.plot(effective_sizes, test_scores, marker="s", label="Test")
        _plt.xlabel("Training samples used")
        metric_name = {
            "accuracy": "Accuracy",
            "balanced_accuracy": "Balanced Accuracy",
            "macro_f1": "Macro-F1",
        }.get(scoring, scoring)
        _plt.ylabel(metric_name)
        ttl = title or f"Learning Curve ({metric_name})"
        _plt.title(ttl)
        _plt.legend()
        _plt.grid(True, linestyle=":", alpha=0.5)
        _plt.tight_layout()
        safe_title = ttl.lower().replace(" ", "_")

        # Use unified output directory structure if subdirs provided
        if subdirs and 'figures' in subdirs:
            out_path = subdirs['figures'] / f"{safe_title}.png"
        else:
            # Fallback to root directory if no subdirs provided
            out_path = f"{safe_title}.png"

        _plt.savefig(out_path, dpi=300, bbox_inches="tight")
        # _plt.show()
        print(f"Saved learning curve -> {out_path}")

        # Optional CSV export
        try:
            from classifier_lib.config import get_config as _get_cfg

            _cfg = _get_cfg()
            save_csv = bool(
                _cfg.get("evaluation", {})
                .get("learning_curve", {})
                .get("save_csv", True)
            )
        except Exception:
            save_csv = True
        if save_csv:
            import csv as _csv

            # Use same directory structure for CSV
            if subdirs and 'tables' in subdirs:
                csv_path = subdirs['tables'] / f"learning_curve_{safe_title}.csv"
            else:
                csv_path = f"learning_curve_{safe_title}.csv"

            with open(csv_path, "w", newline="", encoding="utf-8") as f:
                w = _csv.writer(f)
                w.writerow(["train_samples", "train_score", "test_score", "scoring"])
                for n, tr, te in zip(effective_sizes, train_scores, test_scores):
                    w.writerow([n, tr, te, scoring])
            print(f"Saved learning curve CSV -> {csv_path}")
    except Exception as e: #pylint: disable=broad-except
        print(f"WARNING: Failed to compute learning curve: {e}")


def plot_per_class_pr_curves(
    pr_curves: dict, title: str = "Precision-Recall Curves", figsize=(7, 5),
    subdirs: dict | None = None
):
    """Plot and save per-class Precision-Recall curves from a precomputed dict.

    pr_curves should be a dict mapping class label (str) -> {"precision": [...], "recall": [...], "average_precision": float}
    run_id: optional run identifier for unified directory structure
    subdirs: optional subdirectories dict for unified structure
    """
    try:
        if not isinstance(pr_curves, dict) or not pr_curves:
            print("No PR curves to plot.")
            return None
        plt.figure(figsize=figsize)
        for cls, series in pr_curves.items():
            if not isinstance(series, dict):
                continue
            precision = series.get("precision")
            recall = series.get("recall")
            ap = series.get("average_precision")
            if precision is None or recall is None:
                continue
            try:
                lbl = (
                    f"{cls} (AP={ap:.3f})" if isinstance(ap, (int, float)) else str(cls)
                )
            except Exception:
                lbl = str(cls)
            plt.plot(recall, precision, label=lbl)
        plt.xlabel("Recall")
        plt.ylabel("Precision")
        plt.title(title)
        plt.legend(loc="best", fontsize="small", ncol=2)
        plt.grid(True, linestyle=":", alpha=0.4)
        plt.tight_layout()
        
        # Use unified directory structure if available, otherwise save to root
        filename = f"pr_curves_{title.lower().replace(' ', '_')}.png"
        if subdirs and 'figures' in subdirs:
            out_path = subdirs['figures'] / filename
        else:
            out_path = filename
            
        plt.savefig(out_path, dpi=300, bbox_inches="tight")
        # plt.show()
        print(f"Saved per-class PR curves -> {out_path}")
        return str(out_path)
    except Exception as e: #pylint: disable=broad-except
        print(f"WARNING: Failed to plot PR curves: {e}")
        return None


# package_run_assets function removed - unified output now handled by artefacts/ directory structure
