# A pipeline for classifying error types from buggy code and analysis results.
from pathlib import Path
import json
import csv
import sys
from datetime import datetime, timezone
import warnings
import hashlib
from copy import deepcopy
import numpy as _np
from classifier_lib import config
from classifier_lib.preprocessing import (
    load_and_analyse_dataset,
    prepare_features_and_split,
)
from classifier_lib.train_model import train_model, nested_cv_evaluation
from classifier_lib.analysis import (
    analyse_best_model_features,
    group_and_analyse_errors,
    analyse_model_explainability,
    generate_comparative_shap_analysis,
)
from classifier_lib.output import (
    plot_learning_curve_pipeline,
    print_dataset_summary,
    plot_confusion_matrix,
)
from classifier_lib.testing import test_model_predictions

warnings.filterwarnings("ignore", category=SyntaxWarning)
warnings.filterwarnings("ignore", category=UserWarning)
now = datetime.now()


class Tee:
    def __init__(self, *files, timestamps=False):
        self.files = files
        self.timestamps = timestamps
        self._buffer = ""  # handle partial writes cleanly

    def write(self, obj):
        self._buffer += obj
        while "\n" in self._buffer:
            line, self._buffer = self._buffer.split("\n", 1)
            if self.timestamps and line.strip() != "":
                line = (
                    f"{datetime.now().isoformat(sep=' ', timespec='seconds')} | {line}"
                )
            line_out = line + "\n"
            for f in self.files:
                f.write(line_out)
                f.flush()

    def flush(self):
        if self._buffer:
            line = self._buffer
            self._buffer = ""
            if self.timestamps and line.strip() != "":
                line = (
                    f"{datetime.now().isoformat(sep=' ', timespec='seconds')} | {line}"
                )
            for f in self.files:
                f.write(line)
                f.flush()
        for f in self.files:
            f.flush()


run_data = config.create_run()
run_id, run_dir, subdirs = run_data

# Extract run_id and set up unified directory structure for remaining operations

# redirect all stdout (print) to both console and file
logfile = open(f"{run_dir}/analysis_output.log", "w", encoding="utf-8")
sys.stdout = Tee(sys.stdout, logfile, timestamps=True)
sys.stderr = Tee(sys.stderr, logfile, timestamps=True)

# open log file


def _hash_series(series):
    h = hashlib.sha256()
    for v in series:
        h.update(str(v).encode("utf-8", errors="ignore"))
    return h.hexdigest()


def run_classifiers():
    """
    Main function for comprehensive error type classifier comparison.
    Provides analysis of different ML approaches.
    """
    cfg, args = config.parse_config()

    print("= Error Type Classifier - Comprehensive ML Comparison =")
    print("=" * 60)

    # Load and analyse dataset
    df = load_and_analyse_dataset(args.dataset, cfg)

    # Compute dataset hash/provenance
    if "buggy_code" in df.columns:
        dataset_hash = _hash_series(df["buggy_code"])
    else:
        dataset_hash = _hash_series(df.index)

    # Group errors and analyse effectiveness (unless ablated)
    if args.no_grouping:
        print("Skipping error-type grouping (ablation). Using original labels.")
    else:
        df = group_and_analyse_errors(df)

    # Summarise ablation settings for the run
    print(
        f"Ablation settings -> grouping: {'on' if not args.no_grouping else 'off'} | NoError augmentation: {'on' if not args.no_balance else 'off'}"
    )

    # Prepare features and split data
    (
        X_train, # pylint: disable=invalid-name
        X_test, # pylint: disable=invalid-name
        y_train,
        y_test,
        _,
        _,
        _,
        _,
        test_balanced_df,
    ) = prepare_features_and_split(
        df,
        leakage_safe=True,
        random_state=cfg.get("general", {}).get("random_state"),
        include_no_error=(not args.no_balance),
    )

    # Train and compare multiple models (selection by CV; no test peeking)
    print("\nStarting comprehensive classifier comparison...")
    data = (X_train, y_train, X_test, y_test)

    best_model, all_models, run_info = train_model(
        *data,
        cfg=cfg,
        dataset_hash=dataset_hash,
        test_metadata=test_balanced_df,
        run_data=run_data,
    )

    top_k = int(cfg.get("evaluation", {}).get("significance", {}).get("top_k", 2))

    model_names = all_models.keys()

    def _cv_mean(m):
        # Access CV means from run_info instead of all_models (which contains pipelines)
        cv_means = run_info.get("selection", {}).get("cv_means", {})
        v = cv_means.get(m)
        try:
            return float(v) if v is not None else -_np.inf
        except Exception: #pylint: disable=broad-except
            return -_np.inf

    cand = sorted(model_names, key=_cv_mean, reverse=True)[: max(0, top_k)]

    # Optional Nested CV (uses training portion only; no test usage)
    if bool(cfg.get("evaluation", {}).get("nested_cv", {}).get("enabled", False)):
        nested_cv_evaluation(
            X_train, y_train, cfg=cfg, dataset_hash=dataset_hash, models=cand
        )

    # Seed sensitivity study
    seed_cfg = (
        cfg.get("evaluation", {}).get("seed_sensitivity", {})
        if isinstance(cfg, dict)
        else {}
    )
    if bool(seed_cfg.get("enabled", False)):
        print("\n=== SEED SENSITIVITY STUDY ===")
        seeds = seed_cfg.get("seeds")
        if not seeds:
            base_seed = int(
                seed_cfg.get(
                    "base_seed", cfg.get("general", {}).get("random_state", 25)
                )
            )
            count = int(seed_cfg.get("count", 0))
            offset = int(seed_cfg.get("offset", 100))
            seeds = [base_seed + i * offset for i in range(max(0, count))]
        agg = []
        for i, seed in enumerate(seeds):
            print(
                f"\n[Seed {i+1}/{len(seeds)}] Running comparison with random_state={seed}"
            )
            print(f"Seed results will be saved to: seed_{seed}/")
            run_cfg = deepcopy(cfg)
            run_cfg.setdefault("general", {})["random_state"] = int(seed)
            if bool(seed_cfg.get("skip_final_test", True)):
                run_cfg.setdefault("evaluation", {})["do_final_test"] = False
            run_cfg.setdefault("evaluation", {}).setdefault("seed_sensitivity", {})[
                "in_progress"
            ] = True

            # Create seed-specific subdirectory under the main run
            seed_subdir = run_dir / f"seed_{seed}"
            seed_subdirs = config.create_run_subdirs(seed_subdir)
            seed_run_data = (f"{run_id}_seed{seed}", seed_subdir, seed_subdirs)

            _best_model, _all_models, info = train_model(
                X_train,
                y_train,
                X_test,
                y_test,
                cfg=run_cfg,
                dataset_hash=dataset_hash,
                test_metadata=test_balanced_df,
                run_data=seed_run_data,
            )
            agg.append({"seed": int(seed), "info": info})
        # Simple summary + persist
        try:

            # Compute per-seed best selection score and corresponding CV mean
            per_seed = []
            # Also compute top-k model stability (identity of top-k by selection score)
            top_k = int(seed_cfg.get("top_k", 3))
            topk_lists = []
            for entry in agg:
                inf = entry.get("info", {}) or {}
                sel = (inf.get("selection", {}) or {}).get("scores", {})
                best_name = (inf.get("selection", {}) or {}).get("best_model_name")
                cv_means = (inf.get("selection", {}) or {}).get("cv_means", {})
                run_id_seed = inf.get("run_id")
                # Top-k list for stability
                if isinstance(sel, dict) and sel:
                    ordered = sorted(sel.items(), key=lambda kv: kv[1], reverse=True)
                    topk = [k for k, _ in ordered[: max(0, top_k)]]
                    topk_lists.append(topk)
                else:
                    topk_lists.append([])
                # Best selection score
                if isinstance(sel, dict) and sel:
                    if best_name in sel:
                        sel_best = float(sel.get(best_name))
                    else:
                        try:
                            sel_best = float(max(sel.values()))
                        except Exception: #pylint: disable=broad-except
                            sel_best = None
                else:
                    sel_best = None
                # CV mean for best model
                cv_best = None
                if isinstance(cv_means, dict) and cv_means:
                    if best_name in cv_means:
                        cv_best = float(cv_means.get(best_name))
                    else:
                        try:
                            cv_best = float(max(cv_means.values()))
                        except Exception: #pylint: disable=broad-except
                            cv_best = None
                per_seed.append(
                    {
                        "run_id": run_id_seed,
                        "seed": entry.get("seed"),
                        "best_model": best_name,
                        "selection_score_best": sel_best,
                        "cv_mean_best": cv_best,
                        "topk_selection": (
                            ";".join(topk_lists[-1]) if topk_lists else ""
                        ),
                    }
                )

            scores = [
                row["selection_score_best"]
                for row in per_seed
                if row["selection_score_best"] is not None
            ]
            cv_means_best = [
                row["cv_mean_best"]
                for row in per_seed
                if row["cv_mean_best"] is not None
            ]
            mean = std = ci_l = ci_u = None
            mean_cv = std_cv = ci_l_cv = ci_u_cv = None
            # Top-k stability: fraction of seeds where top-1 is identical to mode; and Jaccard for sets
            top1 = [
                lst.split(";")[0] if lst else None
                for lst in [r.get("topk_selection", "") for r in per_seed]
            ]
            if top1:
                from collections import Counter as _Counter

                cnt = _Counter([t for t in top1 if t])
                mode, mode_n = (None, 0)
                if cnt:
                    mode, mode_n = cnt.most_common(1)[0]
                top1_stability = float(mode_n) / max(1, len([t for t in top1 if t]))
            else:
                top1_stability = None

            # Pairwise Jaccard of top-k sets
            def _jaccard(a, b):
                sa, sb = set(a), set(b)
                inter = len(sa & sb)
                union = len(sa | sb) or 1
                return inter / union

            topk_j = None
            if len(topk_lists) >= 2:
                vals = []
                for i in range(len(topk_lists)):
                    for j in range(i + 1, len(topk_lists)):
                        vals.append(_jaccard(topk_lists[i], topk_lists[j]))
                if vals:
                    topk_j = float(_np.mean(vals))

            if scores:
                arr = _np.array(scores, dtype=float)
                mean = float(arr.mean())
                std = float(arr.std(ddof=1)) if len(arr) > 1 else 0.0
                ci = 1.96 * (std / _np.sqrt(len(arr))) if len(arr) > 1 else 0.0
                ci_l, ci_u = float(mean - ci), float(mean + ci)
                std = round(std, 4) if std is not None else 0.0
                print(
                    f"""\nSeed sensitivity (selection score): 
                    mean={mean:.4f} std={std} over {len(arr)} seeds"""
                )
            if cv_means_best:
                arr_cv = _np.array(cv_means_best, dtype=float)
                mean_cv = float(arr_cv.mean())
                std_cv = float(arr_cv.std(ddof=1)) if len(arr_cv) > 1 else 0.0
                ci_cv = (
                    1.96 * (std_cv / _np.sqrt(len(arr_cv))) if len(arr_cv) > 1 else 0.0
                )
                ci_l_cv, ci_u_cv = float(mean_cv - ci_cv), float(mean_cv + ci_cv)
                std_cv = round(std_cv, 4) if std_cv is not None else 0.0
                print(
                    f"""Seed sensitivity (cv_mean of best):
                    mean={mean_cv:.4f} std={std_cv} over {len(arr_cv)} seeds"""
                )
            # Persist using the main run's ID so packaging can include these artefacts
            # Create a dedicated summary subdirectory for seed sensitivity results
            summary_dir = run_dir / "summary"
            summary_dir.mkdir(exist_ok=True)

            # CSV of per-seed
            csv_path = summary_dir / f"seed_sensitivity_{run_id}.csv"
            with csv_path.open("w", newline="", encoding="utf-8") as f:
                w = csv.writer(f)
                w.writerow(
                    [
                        "run_id",
                        "seed",
                        "best_model",
                        "selection_score_best",
                        "cv_mean_best",
                        "topk_selection",
                    ]
                )
                for row in per_seed:
                    w.writerow(
                        [
                            row.get("run_id"),
                            row.get("seed"),
                            row.get("best_model"),
                            row.get("selection_score_best"),
                            row.get("cv_mean_best"),
                            row.get("topk_selection"),
                        ]
                    )
            print(f"Saved seed sensitivity CSV -> {csv_path}")
            # JSON summary
            js = {
                "run_id": run_id,
                "seeds": [row.get("seed") for row in per_seed],
                "best_models": [row.get("best_model") for row in per_seed],
                "selection_scores_best": [
                    row.get("selection_score_best") for row in per_seed
                ],
                "cv_means_best": [row.get("cv_mean_best") for row in per_seed],
                "topk_selection": [row.get("topk_selection") for row in per_seed],
                "summary": {
                    "selection_score": {
                        "mean": mean,
                        "std": std,
                        "ci95": [ci_l, ci_u],
                        "n": len(scores),
                    },
                    "cv_mean_best": {
                        "mean": mean_cv,
                        "std": std_cv,
                        "ci95": [ci_l_cv, ci_u_cv],
                        "n": len(cv_means_best),
                    },
                    "top1_stability": top1_stability,
                    "topk_jaccard_mean": topk_j,
                },
            }
            json_path = summary_dir / f"seed_sensitivity_{run_id}.json"
            with json_path.open("w", encoding="utf-8") as f:
                json.dump(js, f, indent=2)
            print(f"Saved seed sensitivity summary JSON -> {json_path}")
            # Also write a compact CSV summary for quick reporting
            summary_csv = summary_dir / f"seed_sensitivity_summary_{run_id}.csv"
            with summary_csv.open("w", newline="", encoding="utf-8") as f:
                w = csv.writer(f)
                w.writerow(["metric", "mean", "std", "ci95_l", "ci95_u", "n"])
                w.writerow(["selection_score_best", mean, std, ci_l, ci_u, len(scores)])
                w.writerow(
                    [
                        "cv_mean_best",
                        mean_cv,
                        std_cv,
                        ci_l_cv,
                        ci_u_cv,
                        len(cv_means_best),
                    ]
                )
                w.writerow(
                    ["top1_stability", top1_stability, None, None, None, len(seeds)]
                )
                w.writerow(["topk_jaccard_mean", topk_j, None, None, None, len(seeds)])
            print(f"Saved seed sensitivity summary CSV -> {summary_csv}")
            print(f"\nSeed sensitivity study complete!")
            print(f"Individual seed results saved in: {run_dir}/seed_*/")
            print(f"Summary results saved in: {summary_dir}/")
            # Attach summary paths to run_info for downstream packaging context
            try:
                if isinstance(run_info, dict):
                    run_info.setdefault("seed_sensitivity", {})["summary_csv"] = str(
                        summary_csv
                    )
                    run_info.setdefault("seed_sensitivity", {})["per_seed_csv"] = str(
                        csv_path
                    )
                    run_info.setdefault("seed_sensitivity", {})["summary_json"] = str(
                        json_path
                    )
                    # Also attach computed metrics so they appear in run_summary.json
                    run_info.setdefault("seed_sensitivity", {})["metrics"] = {
                        "selection_score": {
                            "mean": mean,
                            "std": std,
                            "ci95": [ci_l, ci_u],
                            "n": len(scores),
                        },
                        "cv_mean_best": {
                            "mean": mean_cv,
                            "std": std_cv,
                            "ci95": [ci_l_cv, ci_u_cv],
                            "n": len(cv_means_best),
                        },
                        "top1_stability": top1_stability,
                        "topk_jaccard_mean": topk_j,
                    }
            except Exception:
                pass

            # Update aggregate.csv with seed stability metrics
            try:
                agg_path = summary_dir / "aggregate.csv"
                agg_row = {
                    "run_id": run_id,
                    "seed_top1_stability": top1_stability,
                    "seed_topk_jaccard_mean": topk_j,
                    "seed_selection_score_best_mean": mean,
                    "seed_selection_score_best_std": std,
                    "seed_cv_mean_best_mean": mean_cv,
                    "seed_cv_mean_best_std": std_cv,
                    "seed_n": len(seeds) if isinstance(seeds, list) else None,
                }
                if agg_path.exists():
                    # Read existing, union headers, rewrite
                    with agg_path.open("r", newline="", encoding="utf-8") as f:
                        r = csv.DictReader(f)
                        existing_rows = list(r)
                        fieldnames = list(r.fieldnames or [])
                    for k in agg_row.keys():
                        if k not in fieldnames:
                            fieldnames.append(k)
                    # Ensure all existing rows have all fields
                    for row in existing_rows:
                        for k in fieldnames:
                            if k not in row:
                                row[k] = ""
                    existing_rows.append({k: agg_row.get(k, "") for k in fieldnames})
                    with agg_path.open("w", newline="", encoding="utf-8") as f:
                        w = csv.DictWriter(f, fieldnames=fieldnames)
                        w.writeheader()
                        w.writerows(existing_rows)
                else:
                    with agg_path.open("w", newline="", encoding="utf-8") as f:
                        fieldnames = list(agg_row.keys())
                        w = csv.DictWriter(f, fieldnames=fieldnames)
                        w.writeheader()
                        w.writerow(agg_row)
                # Stash path for packaging context later
                try:
                    if isinstance(run_info, dict):
                        run_info.setdefault("aggregate", {})["path"] = str(agg_path)
                except Exception:
                    pass
            except Exception:
                pass
        except (
            ImportError,
            OSError,
            IOError,
            ValueError,
            TypeError,
            AttributeError,
        ) as _e:
            # Already printed basic summary; ignore persistence failure
            pass

    # Test best model predictions on sample cases (safe regardless of test evaluation)
    test_model_predictions(best_model)

    # If final test evaluation was skipped, avoid using the real test set for post-training actions
    raw_dft = (
        run_info.get("do_final_test", True) if isinstance(run_info, dict) else True
    )
    if isinstance(raw_dft, str):
        do_final_test = raw_dft.strip().lower() in {"true", "1", "yes", "y", "t"}
    else:
        do_final_test = bool(raw_dft)

    # Track figure paths for packaging
    extra_paths: list[str] = []

    if do_final_test:
        # Determine label order from model if available
        class_names = None
        if hasattr(best_model, "classes_"):
            class_names = list(best_model.classes_)
        elif (
            hasattr(best_model, "named_steps")
            and "classifier" in getattr(best_model, "named_steps", {})
            and hasattr(best_model.named_steps["classifier"], "classes_")
        ):
            class_names = list(best_model.named_steps["classifier"].classes_)
        if class_names is None:
            class_names = sorted(set(y_train) | set(y_test))
        # Get best model name for title
        best_model_name = run_info.get('selection', {}).get('best_model_name', 'Best Model')
        cm_path = plot_confusion_matrix(
            y_test,
            best_model.predict(X_test),
            class_names=class_names,
            title=f"{best_model_name} Confusion Matrix",
            subdirs=subdirs,
            run_id=run_id,
        )
        if isinstance(cm_path, str):
            extra_paths.append(cm_path)
        # Learning curve (train/test score vs sample size) if enabled in config
        lc_cfg = (
            cfg.get("evaluation", {}).get("learning_curve", {})
            if isinstance(cfg, dict)
            else {}
        )
        if bool(lc_cfg.get("enabled", True)):
            lc_scoring = str(lc_cfg.get("scoring", "accuracy"))
            lc_sizes = lc_cfg.get("train_sizes")  # list of fractions or ints
            lc_seed = lc_cfg.get(
                "random_state", cfg.get("general", {}).get("random_state")
            )
            # Get best model name for title
            best_model_name = run_info.get('selection', {}).get('best_model_name', 'Best Model')
            plot_learning_curve_pipeline(
                best_model,
                X_train,
                y_train,
                X_test,
                y_test,
                train_sizes=lc_sizes,
                scoring=lc_scoring,
                title=f"Learning Curve - {best_model_name}",
                random_state=lc_seed,
                subdirs=subdirs,
            )
        # Explainability analysis of best model (uses test set)
        # Extract model performance for SHAP decision making
        model_performance = {}
        if 'selection' in run_info and 'cv_means' in run_info['selection']:
            model_performance = run_info['selection']['cv_means']

        # Get best model name from the correct location in run_info
        best_model_name = run_info.get('selection', {}).get('best_model_name', 'Unknown')

        analyse_model_explainability(
            best_model,
            X_test,
            y_test,
            use_shap=cfg.get("explainability", {}).get("use_shap", True),
            shap_sample_size=cfg.get("explainability", {}).get("shap_sample_size", 150),
            cfg=cfg,
            run_id=run_id,
            model_name=best_model_name,
            all_models_performance=model_performance,
        )

        # Comparative SHAP analysis across top models
        if cfg.get("explainability", {}).get("use_shap", True):
            generate_comparative_shap_analysis(
                all_models, model_performance, X_test, y_test,
                cfg, subdirs, run_id, top_n=3
            )
        # Include slice metrics file if present
        try:
            rid = run_info.get("run_id") if isinstance(run_info, dict) else None
            if rid:

                # Use unified directory structure if available
                if subdirs:
                    p = subdirs["tables"] / f"slices_{rid}.csv"
                else:
                    p = Path("metrics") / f"slices_{rid}.csv"
                if p.exists():
                    extra_paths.append(str(p))
        except Exception:  # pylint: disable=broad-except
            pass
    else:
        print(
            "\nSkipping confusion matrix and SHAP explainability (final test evaluation was skipped due to invalid CV scores or user override)."
        )

    # Export ablation settings and efficiency tables for packaging/reporting
    try:
        # Use unified directory structure if available
        if subdirs:
            export_dir = subdirs["tables"]
        else:
            export_dir = Path("metrics")
            export_dir.mkdir(exist_ok=True)

        rid = run_info.get("run_id") if isinstance(run_info, dict) else None
        rid = rid or datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        # Ablation settings
        abl = cfg.get("experiment", {}) if isinstance(cfg, dict) else {}
        abl_csv = export_dir / f"ablation_settings_{rid}.csv"
        with abl_csv.open("w", newline="", encoding="utf-8") as f:
            w = csv.writer(f)
            w.writerow(["key", "value"])
            for k, v in abl.items():
                w.writerow([k, v])
        abl_json = export_dir / f"ablation_settings_{rid}.json"
        with abl_json.open("w", encoding="utf-8") as f:
            json.dump(abl, f, indent=2)
        extra_paths.extend([str(abl_csv), str(abl_json)])
        # Efficiency by model if available in run_info
        eff = (run_info.get("efficiency") or {}) if isinstance(run_info, dict) else {}
        tt = (
            (eff.get("training_time_sec_by_model") or {})
            if isinstance(eff, dict)
            else {}
        )
        ct = (eff.get("cv_time_sec_by_model") or {}) if isinstance(eff, dict) else {}
        it = (
            (eff.get("inference_time_sec_by_model") or {})
            if isinstance(eff, dict)
            else {}
        )
        ipm = (
            (eff.get("inference_time_per_sample_ms_by_model") or {})
            if isinstance(eff, dict)
            else {}
        )
        pc = (eff.get("param_count_by_model") or {}) if isinstance(eff, dict) else {}
        if any([tt, ct, it, ipm, pc]):
            eff_csv = export_dir / f"efficiency_{rid}.csv"
            # Union of model names across all dicts
            models = (
                set(tt.keys())
                | set(ct.keys())
                | set(it.keys())
                | set(ipm.keys())
                | set(pc.keys())
            )
            with eff_csv.open("w", newline="", encoding="utf-8") as f:
                w = csv.writer(f)
                w.writerow(
                    [
                        "model",
                        "fit_time_sec",
                        "cv_time_sec",
                        "inference_time_sec",
                        "inference_time_per_sample_ms",
                        "param_count",
                    ]
                )
                for m in sorted(models):
                    w.writerow(
                        [
                            m,
                            tt.get(m),
                            ct.get(m),
                            it.get(m),
                            ipm.get(m),
                            pc.get(m),
                        ]
                    )
            extra_paths.append(str(eff_csv))
        # Calibration bins export (pre/post)
        cal = (run_info.get("calibration") or {}) if isinstance(run_info, dict) else {}
        for tag in ["pre", "post"]:
            obj = cal.get(tag)
            if isinstance(obj, dict) and obj.get("bin_counts") is not None:
                cal_csv = export_dir / f"calibration_{tag}_{rid}.csv"
                with cal_csv.open("w", newline="", encoding="utf-8") as f:
                    w = csv.writer(f)
                    w.writerow(["bin", "count", "mean_conf", "accuracy"])
                    for i, (cnt, conf, acc) in enumerate(
                        zip(
                            obj.get("bin_counts"),
                            obj.get("bin_conf"),
                            obj.get("bin_acc"),
                        )
                    ):
                        w.writerow([i, cnt, conf, acc])
                extra_paths.append(str(cal_csv))
        # Include aggregate.csv in packaged assets if present
        try:
            agg_p = export_dir / "aggregate.csv"
            if agg_p.exists():
                extra_paths.append(str(agg_p))
        except Exception:
            pass
        # Threats to validity narrative packaged with the run
        try:
            doc_path = validity_notes(run_info, cfg, export_dir, rid)
            extra_paths.append(str(doc_path))

            if isinstance(run_info, dict):
                run_info.setdefault("docs", {})["threats_to_validity"] = str(doc_path)
        except Exception as _e:
            print(f"WARNING: Failed to generate threats to validity notes: {_e}")
    except Exception as _e:
        print(f"WARNING: Failed to export ablation/efficiency tables: {_e}")

    # Package artefacts, figures, and tables for the run
    try:
        pkg_cfg = cfg.get("packaging", {}) if isinstance(cfg, dict) else {}
        if bool(pkg_cfg.get("enabled", True)):
            # Include logfile as provenance
            extra_paths.append(f"analysis_output-{now}.log")
            # Include seed sensitivity summary files if they exist
            try:
                summary_dir = run_dir / "summary"
                if summary_dir.exists():
                    for summary_file in summary_dir.glob("seed_sensitivity_*"):
                        extra_paths.append(str(summary_file))
            except Exception:
                pass
            # Include slice metrics if available
            try:
                sl = (
                    (run_info.get("slices") or {}) if isinstance(run_info, dict) else {}
                )
                p = sl.get("metrics_csv") if isinstance(sl, dict) else None
                if isinstance(p, str):
                    extra_paths.append(p)
            except Exception:
                pass
            # All outputs are now unified in the artefacts directory structure
            print(f"All run outputs available in unified directory -> {run_dir}")
    except Exception as _e:
        print(f"WARNING: Packaging step failed: {_e}")

    # Analyse best model features if it supports feature analysis
    best_model_name = run_info.get('selection', {}).get('best_model_name', 'Unknown')
    if best_model and best_model_name in all_models:
        print(f"\n{best_model_name} Feature Analysis:")
        analyse_best_model_features(all_models[best_model_name], best_model_name)
    # Print summary
    print_dataset_summary(df)


def validity_notes(run_info, cfg, metrics_dir, rid):
    """Generate a markdown-formatted list of threats to validity notes."""

    exp = cfg.get("experiment", {}) if isinstance(cfg, dict) else {}
    eval_cfg = cfg.get("evaluation", {}) if isinstance(cfg, dict) else {}
    dft_flag = run_info.get("do_final_test") if isinstance(run_info, dict) else False
    nested_on = bool(((eval_cfg.get("nested_cv") or {}).get("enabled", False)))
    cal_cfg = (
        (eval_cfg.get("calibration_posthoc") or {})
        if isinstance(eval_cfg, dict)
        else {}
    )
    ss = (run_info.get("seed_sensitivity") or {}) if isinstance(run_info, dict) else {}
    ss_metrics = ss.get("metrics") if isinstance(ss, dict) else None
    doc_path = metrics_dir / f"threats_to_validity_{rid}.md"
    content = []
    content.append(f"# Threats to Validity (Run {rid})\n")
    content.append(
        "This section summarises potential threats to the validity of the reported results and the steps taken to mitigate them."
    )
    # Internal validity
    content.append("\n## Internal validity")
    content.append(
        "- Data leakage: The split is performed on original samples before any balancing or feature construction; test data are never used for model selection. Nested cross-validation is available for unbiased performance estimation."
        if nested_on
        else "- Data leakage: The split is performed on original samples before balancing and feature construction; model selection uses only training folds. Nested CV is available but may be disabled for runtime reasons."
    )
    # Final test gating
    content.append(
        "- Final test gating: Final test evaluation is only performed when cross-validation is valid or when explicitly enabled. This prevents optimistic leakage into the test set."
        if dft_flag
        else "A final test evaluation was not performed due to invalid CV scores or user override; thus, no information from the test set was used during model selection."
    )
    # Post-hoc Calibration
    content.append(
        f"- Calibration: Post-hoc calibration using {cal_cfg.get('method', 'isotonic')} regression was applied using {cal_cfg.get('cv', 5)}-fold CV on the training set to improve probability estimates."
        if bool(cal_cfg.get("enabled", False))
        else "- Calibration: No post-hoc calibration was applied; predicted probabilities may be miscalibrated."
    )

    # Construct validity
    content.append("\n## Construct validity")
    content.append(
        f"- Label space and grouping: Grouping of error types is {'enabled' if exp.get('grouping', True) else 'disabled'}; this trades finer granularity for improved sample efficiency. An ablation without grouping is available via CLI and reported separately."
    )
    content.append(
        f"- Class balance: Inclusion of synthetic/structural 'NoError' samples is {'enabled' if exp.get('include_no_error', True) else 'disabled'}; this can affect decision thresholds. An ablation without NoError augmentation is available."
    )
    # External validity
    content.append("\n## External validity")
    content.append(
        "- Dataset representativeness: The dataset originates from controlled generations and may not capture the full variability of real-world code errors. Distribution shift to new sources, languages, or styles may degrade performance."
    )
    content.append(
        "- Generalisation risk: Token and structural features may overfit to dataset-specific artefacts; future work should validate on independent corpora."
    )
    # Statistical conclusion validity
    content.append("\n## Statistical conclusion validity")
    if isinstance(ss_metrics, dict):
        sc = (
            ss_metrics.get("selection_score", {})
            if isinstance(ss_metrics, dict)
            else {}
        )
        top1 = ss_metrics.get("top1_stability")
        content.append(
            f"- Randomness sensitivity: Seed study indicates selection-score mean={sc.get('mean')} (n={sc.get('n')}), with top-1 model stability={top1}. This quantifies variability across seeds."
        )
    else:
        content.append(
            "- Randomness sensitivity: A seed sensitivity study can be enabled to quantify variability across random initialisations."
        )
    content.append(
        "- Cross-validation: K-fold CV is used for model selection; optional nested CV provides an unbiased outer estimate. Bootstrap confidence intervals are computed for key test metrics when the final test is run."
    )
    # Calibration validity
    content.append("\n## Calibration validity")
    content.append(
        "- Probability calibration: Pre- and optional post-hoc calibration (isotonic or sigmoid) are evaluated via Brier score and reliability diagrams. Miscalibration can affect decision-making and downstream tooling."
    )
    # Implementation validity
    content.append("\n## Implementation and reproducibility")
    content.append(
        "- Reproducibility: Random states are centrally controlled via configuration and CLI. Runs are logged with configuration, seeds, and artefacts packaged for provenance."
    )
    content.append(
        "- Complexity and efficiency: Training/inference times and parameter counts are recorded per model to contextualise performance vs cost."
    )
    # Residual risks
    content.append("\n## Residual risks")
    content.append(
        "- Preprocessing assumptions (e.g., tokenisation, AST parsing) may introduce biases. Edge cases in code formatting or unparsable snippets can affect feature extraction and downstream predictions."
    )
    content.append(
        "- Class boundary ambiguity (e.g., SyntaxError vs ValueError) can limit ceiling performance; confusion analysis highlights these cases."
    )
    with doc_path.open("w", encoding="utf-8") as f:
        f.write("\n".join(content) + "\n")
    return doc_path


if __name__ == "__main__":
    run_classifiers()
