import os
import time
import numpy as np
from sklearn.metrics import (
    classification_report,
    accuracy_score,
    f1_score,
    balanced_accuracy_score,
)
from classifier_lib.preprocessing import _cfg_seed, _cv_splitter_seed, _maybe_seed_process
from classifier_lib.data import create_test_cases, create_feature_text
from classifier_lib.analysis import analyse_model_predictions_detailed
from classifier_lib.config import get_effective_n_jobs


def test_model_predictions(trained_pipeline):
    """Test the model with sample code patterns and detailed analysis."""
    print("\n--- Testing Predictions on Sample Code Patterns ---")

    # Run basic test cases
    test_cases = create_test_cases()
    test_features_dicts = [create_feature_text(case) for case in test_cases]

    # Convert feature dictionaries to DataFrame format using the same function as training
    import pandas as pd
    from classifier_lib.preprocessing import _convert_features_to_dataframe

    # Create a temporary DataFrame with the test cases
    test_cases_df = pd.DataFrame(test_cases)
    test_features_series = pd.Series(test_features_dicts)

    # Use the same conversion function as training to get proper feature names
    test_df = _convert_features_to_dataframe(test_cases_df, test_features_series)

    # Extract only the feature columns (those starting with 'feat_')
    feature_cols = [col for col in test_df.columns if col.startswith('feat_')]
    test_features_only = test_df[feature_cols]

    # Ensure all expected feature columns are present (fill missing with 0)
    expected_features = getattr(trained_pipeline, '_feature_names_in', None)
    if expected_features is not None:
        for col in expected_features:
            if col not in test_features_only.columns:
                test_features_only[col] = 0
        test_features_only = test_features_only[expected_features]

    predictions = trained_pipeline.predict(test_features_only)

    for i, (case, prediction) in enumerate(zip(test_cases, predictions)):
        print(f"\nTest Case {i+1}:")
        print(f"Code: {case['code']}")
        print(f"AST Message: {case['ast_message']}")
        print(f"Predicted Error Type: {prediction}")

    # Run detailed analysis
    analyse_model_predictions_detailed(trained_pipeline)


def train_and_evaluate(
    pipeline,
    X_train,
    y_train,
    X_test,
    y_test,
    X_cv,
    y_cv,
    model_name="Model",
    *,
    evaluate_on_test: bool = True,
    cfg: dict | None = None,
):
    """
    Trains the pipeline and evaluates its performance.
    Cross-validation is performed ONLY on the training portion (X_cv, y_cv) to avoid
    subtle information leakage from the held-out test set.
    If evaluate_on_test=False, test-set evaluation is skipped to prevent peeking.
    """
    from sklearn.model_selection import (
        StratifiedKFold,
        RepeatedStratifiedKFold,
        cross_val_score,
    )
    from classifier_lib.preprocessing import _maybe_seed_process

    _maybe_seed_process(cfg)

    # Timing: fit and CV measured separately for selection and reporting
    t0 = time.perf_counter()
    print(f"Training {model_name} on {len(X_train)} samples...")
    pipeline.fit(X_train, y_train)
    fit_time = time.perf_counter() - t0
    print(f"{model_name} training complete. (fit_time={fit_time:.3f}s)")

    # Optional test set evaluation (can be skipped for model selection)
    test_metrics = {
        "test_accuracy": None,
        "test_macro_f1": None,
        "test_micro_f1": None,
        "test_balanced_accuracy": None,
    }
    if evaluate_on_test:
        print(
            f"\n=== {model_name.upper()} EVALUATION (Test set: {len(y_test)} samples) ==="
        )
        y_pred = pipeline.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        macro_f1 = f1_score(y_test, y_pred, average="macro", zero_division=0)
        micro_f1 = f1_score(y_test, y_pred, average="micro", zero_division=0)
        bal_acc = balanced_accuracy_score(y_test, y_pred)
        print(f"Test Accuracy: {accuracy:.4f}")
        print(f"Macro F1:      {macro_f1:.4f}")
        print(f"Micro F1:      {micro_f1:.4f}")
        print(f"Balanced Acc:  {bal_acc:.4f}")

        print(f"\n{model_name} Classification Report:")
        print(classification_report(y_test, y_pred, zero_division=0))
        test_metrics = {
            "test_accuracy": accuracy,
            "test_macro_f1": macro_f1,
            "test_micro_f1": micro_f1,
            "test_balanced_accuracy": bal_acc,
        }

    # Cross-validation on training data only, with fold safety
    print(
        f"\n=== {model_name.upper()} CROSS-VALIDATION (Training data: {len(y_cv)} samples) ==="
    )

    # Compute safe number of splits based on minimum per-class count
    try:
        min_class_count = int(getattr(y_cv, "value_counts", lambda: None)().min())  
    except (AttributeError, TypeError, ValueError):
        # Fallback if y_cv is not a pandas Series
        try:
            from collections import Counter

            min_class_count = min(Counter(y_cv).values())
        except (TypeError, ValueError):
            min_class_count = 0

    cv_mean = None
    cv_std = None
    cv_fold_scores = None
    cv_used_splits = None
    cv_used_repeats = None
    cv_scoring = None
    cv_time = None
    if min_class_count >= 2 and len(set(y_cv)) > 1:
        # Resolve CV configuration
        eval_cfg = (cfg or {}).get("evaluation", {})
        cv_cfg = eval_cfg.get("cv", {})
        requested_splits = int(cv_cfg.get("n_splits", 5))
        requested_repeats = int(cv_cfg.get("repeats", 1))
        scoring = str(cv_cfg.get("scoring", "accuracy"))
        # Map friendly names to sklearn scorer strings
        scoring_sklearn = scoring
        if scoring.lower() == "macro_f1":
            scoring_sklearn = "f1_macro"
        elif scoring.lower() == "balanced_accuracy":
            scoring_sklearn = "balanced_accuracy"
        else:
            scoring_sklearn = "accuracy"
        # Bound splits by data safety
        cv_folds = min(requested_splits, int(min_class_count))
        cv_folds = max(cv_folds, 2)
        base_seed = _cfg_seed(cfg, 25)
        split_seed = _cv_splitter_seed(cfg, base_seed)
        if requested_repeats > 1:
            cv = RepeatedStratifiedKFold(
                n_splits=cv_folds, n_repeats=requested_repeats, random_state=split_seed
            )
            cv_used_repeats = requested_repeats
        else:
            cv = StratifiedKFold(
                n_splits=cv_folds, shuffle=True, random_state=split_seed
            )
            cv_used_repeats = 1
        cv_used_splits = cv_folds
        cv_scoring = scoring
        # Resource control with memory-intensive model detection
        n_jobs = get_effective_n_jobs(cfg)

        # Check if this is a memory-intensive model based on model name
        memory_intensive_models = ["neural network", "svm (rbf)", "gradient boosting", "xgboost"]
        is_memory_intensive = any(mem_model in model_name.lower() for mem_model in memory_intensive_models)

        if is_memory_intensive:
            # Use conservative settings for memory-intensive models
            cv_n_jobs = min(n_jobs, 4)  # Cap at 4 processes
            print(f"  Using conservative parallelism for {model_name}: n_jobs={cv_n_jobs}")
        else:
            cv_n_jobs = n_jobs

        t_cv0 = time.perf_counter()
        cv_scores = cross_val_score(
            pipeline, X_cv, y_cv, cv=cv, scoring=scoring_sklearn, n_jobs=cv_n_jobs
        )
        cv_time = time.perf_counter() - t_cv0
        cv_fold_scores = [float(s) for s in cv_scores]
        cv_mean = float(cv_scores.mean())
        cv_std = float(cv_scores.std(ddof=1)) if len(cv_scores) > 1 else 0.0
        # Formatted summary
        total_folds = len(cv_scores)
        rep_info = (
            f" (repeats={cv_used_repeats})"
            if cv_used_repeats and cv_used_repeats > 1
            else ""
        )
        print(
            f"{cv_folds}-Fold{rep_info} CV {scoring}: {cv_mean:.4f} (+/- {cv_std * 2:.4f}) over {total_folds} folds"
        )
        # Heuristic stability warning
        if cv_std > float(cv_cfg.get("warn_std_threshold", 0.15)):
            print(
                f"WARNING: High variance in CV scores for {model_name} (std={cv_std:.3f}) - model may be unstable"
            )
    else:
        print(
            "WARNING: Skipping stratified CV (min per-class samples < 2 or single-class)."
        )

    return pipeline, {
        **test_metrics,
        "cv_mean_accuracy": cv_mean,
        "cv_std_accuracy": cv_std,
        "cv_fold_scores": cv_fold_scores,
        "cv_used_splits": cv_used_splits,
        "cv_used_repeats": cv_used_repeats,
        "cv_scoring": cv_scoring,
        "fit_time_sec": float(fit_time),
        "cv_time_sec": float(cv_time) if cv_time is not None else None,
    }


def hyperparameter_search(
    pipeline,
    classifier_type: str,
    X_train,
    y_train,
    n_iter: int = 4,
    random_state: int = 25,
    *,
    cfg: dict | None = None,
):
    """Run bounded hyperparameter search for selected classifier types (Step 4).

    - Expanded search spaces for key models with guardrails
    - CV folds respect evaluation.cv settings but are clipped by class counts
    - Uses scoring mapped from evaluation.cv.scoring
    - Respects tuning.n_jobs and n_iter caps
    """
    from sklearn.model_selection import (
        RandomizedSearchCV,
        StratifiedKFold,
        RepeatedStratifiedKFold,
    )

    # Expanded parameter distributions (kept moderate for practicality)
    param_distributions = {
        "logistic_regression": {
            # liblinear supports l1/l2; keep l2 for stability with class_weight
            "classifier__C": [0.01, 0.03, 0.1, 0.3, 1.0, 3.0, 10.0, 30.0],
        },
        "random_forest": {
            "classifier__n_estimators": [200, 300, 500],
            "classifier__max_depth": [None, 10, 15, 20],
            "classifier__min_samples_split": [2, 5, 10],
            "classifier__min_samples_leaf": [1, 2, 4],
            "classifier__max_features": ["sqrt", "log2", None],
        },
        "svm_linear": {
            "classifier__C": [0.01, 0.03, 0.1, 0.3, 1.0, 3.0, 10.0, 30.0, 100.0],
        },
        "svm_rbf": {
            "classifier__C": [0.1, 0.3, 1.0, 3.0, 10.0, 30.0],
            "classifier__gamma": ["scale", "auto", 1e-3, 1e-2, 1e-1],
        },
        "gradient_boosting": {
            "classifier__n_estimators": [100, 150, 200],
            "classifier__learning_rate": [0.05, 0.1, 0.2],
            "classifier__max_depth": [2, 3, 4],
            "classifier__subsample": [0.6, 0.8, 1.0],
        },
        "neural_network": {
            "classifier__hidden_layer_sizes": [(50,), (100,), (50, 50), (100, 50)],
            "classifier__alpha": [1e-5, 1e-4, 1e-3, 1e-2],
            "classifier__learning_rate_init": [1e-3, 5e-4, 1e-4],
            "classifier__activation": ["relu", "tanh"],
        },
        # Wrapped by LabelEncodingClassifier -> tune base_estimator__*
        "knn": {
            "classifier__base_estimator__n_neighbors": [3, 5, 7, 9, 11],
            "classifier__base_estimator__weights": ["uniform", "distance"],
            "classifier__base_estimator__metric": [
                "euclidean",
                "manhattan",
                "minkowski",
                "cosine",
            ],
        },
        
        "xgboost": {
            "classifier__n_estimators": [200, 400],
            "classifier__learning_rate": [0.05, 0.1, 0.2],
            "classifier__max_depth": [4, 6, 8],
            "classifier__subsample": [0.6, 0.8, 1.0],
            "classifier__colsample_bytree": [0.6, 0.8, 1.0],
            "classifier__reg_lambda": [0.0, 1.0, 5.0],
            "classifier__reg_alpha": [0.0, 0.5, 1.0],
        },
        "hist_gradient_boosting": {
            "classifier__learning_rate": [0.05, 0.1, 0.2],
            "classifier__max_leaf_nodes": [31, 63, 127],
            "classifier__l2_regularization": [0.0, 0.1, 1.0],
        },
    }
    if classifier_type not in param_distributions:
        return pipeline, {}, None

    # Ensure n_iter doesn't exceed total possible combinations
    total_combos = 1
    for v in param_distributions[classifier_type].values():
        total_combos *= len(v)
    effective_iter = min(n_iter, total_combos)

    # Determine safe CV folds for tuning
    try:
        min_class_count = int(getattr(y_train, "value_counts", lambda: None)().min())  
    except (AttributeError, TypeError, ValueError):
        try:
            from collections import Counter

            min_class_count = min(Counter(y_train).values())
        except (TypeError, ValueError):
            min_class_count = 0

    if min_class_count < 2 or len(set(y_train)) <= 1:
        print(
            "Skipping hyperparameter search (insufficient per-class samples for stratified CV)."
        )
        return pipeline, {}, None

    # Respect requested CV folds but clip by min_class_count
    eval_cv_cfg = (
        (cfg or {}).get("evaluation", {}).get("cv", {}) if isinstance(cfg, dict) else {}
    )
    requested_splits = int(eval_cv_cfg.get("n_splits", 5))
    n_splits = max(2, min(requested_splits, int(min_class_count)))

    # Use centralised n_jobs logic
    n_jobs = get_effective_n_jobs(cfg)
    cpu = max(1, (os.cpu_count() or 2))
    req_n_jobs = cfg.get("tuning", {}).get("n_jobs", -1) if cfg else -1

    # Seed handling
    base_seed = _cfg_seed(cfg, random_state)
    split_seed = _cv_splitter_seed(cfg, base_seed)

    # Scoring mapping from evaluation.cv.scoring
    scoring_name = str(eval_cv_cfg.get("scoring", "accuracy"))
    if scoring_name.lower() == "macro_f1":
        scoring_sklearn = "f1_macro"
    elif scoring_name.lower() == "balanced_accuracy":
        scoring_sklearn = "balanced_accuracy"
    else:
        scoring_sklearn = "accuracy"

    # Build splitter respecting repeats
    repeats = int(eval_cv_cfg.get("repeats", 1))
    if repeats > 1:
        inner_cv = RepeatedStratifiedKFold(
            n_splits=n_splits, n_repeats=repeats, random_state=split_seed
        )
    else:
        inner_cv = StratifiedKFold(
            n_splits=n_splits, shuffle=True, random_state=split_seed
        )

    # Avoid nested parallelism: if the estimator has n_jobs, force it to 1 during search
    try:
        if "classifier__n_jobs" in pipeline.get_params().keys():
            pipeline = pipeline.set_params(classifier__n_jobs=1)
    except (AttributeError, ValueError, TypeError):
        pass

    # Memory-intensive models need conservative parallelism to avoid worker termination
    memory_intensive_models = ["neural_network", "svm_rbf", "gradient_boosting", "xgboost"]
    is_memory_intensive = classifier_type in memory_intensive_models

    if is_memory_intensive:
        # Use conservative settings for memory-intensive models
        search_n_jobs = min(n_jobs, 4)  # Cap at 4 processes
        pre_dispatch_val = min(6, search_n_jobs + 2)  # Conservative pre-dispatch
        print(f"  Using conservative parallelism for {classifier_type}: n_jobs={search_n_jobs}, pre_dispatch={pre_dispatch_val}")
    else:
        # Use full parallelism for lightweight models
        search_n_jobs = n_jobs
        pre_dispatch_val = max(2, int(2 * n_jobs)) if isinstance(n_jobs, int) else 2

    search = RandomizedSearchCV(
        estimator=pipeline,
        param_distributions=param_distributions[classifier_type],
        n_iter=effective_iter,
        scoring=scoring_sklearn,
        cv=inner_cv,
        verbose=0,
        n_jobs=search_n_jobs,
        random_state=base_seed,
        refit=True,
        error_score=np.nan,
        pre_dispatch=pre_dispatch_val,
    )
    print(
        f"Hyperparameter search for {classifier_type} with {effective_iter} iterations (CV={n_splits}, scoring={scoring_name})..."
    )
    search.fit(X_train, y_train)
    try:
        print(f"  Best CV score ({scoring_name}): {float(search.best_score_):.4f}")
    except (TypeError, ValueError):
        pass
    return search.best_estimator_, search.best_params_, search.best_score_
