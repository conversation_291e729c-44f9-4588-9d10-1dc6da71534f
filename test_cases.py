#!/usr/bin/env python3

"""
Test the feature extraction for our test cases
"""

import sys
import os
from classifier_lib.data import create_feature_text

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_individual_cases():
    """Test cases from the classifier"""
    print("Testing feature extraction for test cases...")

    test_cases = [
        {
            "code": "print(unknown_var)",
            "correct_code": "unknown_var = 'hello'\nprint(unknown_var)",
            "ast_message": "name 'unknown_var' is not defined",
            "error_description": "Variable not defined",
        },
        {
            "code": 'if x > 5\n    print("greater")',
            "correct_code": 'if x > 5:\n    print("greater")',
            "ast_message": "expected ':'",
            "error_description": "Missing colon in if statement",
        },
        {
            "code": 'result = "Number: " + 25',
            "correct_code": 'result = "Number: " + str(25)',
            "ast_message": 'can only concatenate str (not "int") to str',
            "error_description": "Type mismatch in concatenation",
        },
        {
            "code": 'def greet(name):\n    return f"Hello, {name}!"\n\nprint(greet("World"))',
            "correct_code": "",  # No correct code needed
            "ast_message": "",
            "error_description": "No errors found",
        },
    ]

    expected_types = ["VariableError", "SyntaxError", "TypeError", "NoError"]

    for i, (case, expected) in enumerate(zip(test_cases, expected_types)):
        print(f"\n=== Test Case {i+1}: Expected {expected} ===")
        print(f"Buggy: {repr(case['code'])}")
        print(f"Correct: {repr(case['correct_code'])}")

        feature_text = create_feature_text(case)
        print(f"Features: {feature_text}")

        # Analyze features for expected patterns
        if (
            expected == "VariableError"
            and "variable_declaration_removed" in feature_text
        ):
            print("✅ Contains variable declaration pattern")
        elif expected == "SyntaxError" and "colon_missing" in feature_text:
            print("✅ Contains syntax error pattern")
        elif expected == "TypeError" and "type_conversion_missing" in feature_text:
            print("✅ Contains type conversion pattern")
        elif expected == "NoError" and "code_structure" in feature_text:
            print("✅ Contains structural analysis")
        else:
            print("❌ Missing expected pattern for", expected)


if __name__ == "__main__":
    test_individual_cases()
